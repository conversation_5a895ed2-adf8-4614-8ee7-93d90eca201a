'use client'

import Link from 'next/link'
import {
  Database,
  Server,
  Cloud,
  FileText,
  Wifi,
  CheckCircle,
  AlertCircle,
  Clock,
  Plus,
  ArrowRight,
  Activity,
  HardDrive,
  Zap,
  Building2,
  Users,
  MapPin,
  TrendingUp,
  BarChart3,
  Shield,
  Globe,
  Briefcase,
  Heart,
  GraduationCap,
  TreePine,
  Home,
  Car,
  Droplets,
  Wheat,
  ShoppingBag,
  Camera,
  Stethoscope,
  AlertTriangle,
  Scale,
  PieChart,
  CreditCard,
  Eye,
  Grid3X3,
  List,
  Search,
  Filter
} from 'lucide-react'
import { useState } from 'react'

export default function CollectionSystemPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'compact'>('compact')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  // 全市24个数据来源单位及其采集的数据类型
  const dataSourceUnits = [
    {
      id: 1,
      name: '市政府办公厅',
      icon: Building2,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '45K',
      lastSync: '5分钟前',
      dataTypes: [
        { name: '政务公开数据', records: '15K', status: 'connected', type: 'API' },
        { name: '会议纪要数据', records: '20K', status: 'connected', type: 'Database' },
        { name: '公文流转数据', records: '10K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 2,
      name: '发展改革委',
      icon: TrendingUp,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '128K',
      lastSync: '3分钟前',
      dataTypes: [
        { name: '项目投资数据', records: '35K', status: 'connected', type: 'Database' },
        { name: '经济指标数据', records: '42K', status: 'connected', type: 'API' },
        { name: '规划数据', records: '28K', status: 'connected', type: 'File' },
        { name: '价格监测数据', records: '23K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 3,
      name: '教育局',
      icon: GraduationCap,
      status: 'warning',
      totalSources: 5,
      activeSources: 4,
      totalRecords: '892K',
      lastSync: '2小时前',
      dataTypes: [
        { name: '学生学籍数据', records: '456K', status: 'connected', type: 'Database' },
        { name: '教师信息数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '学校基础数据', records: '234K', status: 'error', type: 'API' },
        { name: '考试成绩数据', records: '78K', status: 'connected', type: 'File' },
        { name: '教育资源数据', records: '35K', status: 'connected', type: 'Database' }
      ]
    },
    {
      id: 4,
      name: '科技局',
      icon: Zap,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '67K',
      lastSync: '8分钟前',
      dataTypes: [
        { name: '科技项目数据', records: '28K', status: 'connected', type: 'Database' },
        { name: '专利申请数据', records: '23K', status: 'connected', type: 'API' },
        { name: '科技企业数据', records: '16K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 5,
      name: '工信局',
      icon: Server,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '234K',
      lastSync: '1分钟前',
      dataTypes: [
        { name: '工业企业数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '产业园区数据', records: '45K', status: 'connected', type: 'API' },
        { name: '信息化项目数据', records: '67K', status: 'connected', type: 'Database' },
        { name: '节能减排数据', records: '33K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 6,
      name: '公安局',
      icon: Shield,
      status: 'connected',
      totalSources: 6,
      activeSources: 6,
      totalRecords: '2.4M',
      lastSync: '实时',
      dataTypes: [
        { name: '人口基础信息', records: '1.2M', status: 'connected', type: 'Database' },
        { name: '户籍管理数据', records: '456K', status: 'connected', type: 'Database' },
        { name: '身份证数据', records: '567K', status: 'connected', type: 'Database' },
        { name: '治安管理数据', records: '89K', status: 'connected', type: 'API' },
        { name: '交通违法数据', records: '67K', status: 'connected', type: 'Database' },
        { name: '案件管理数据', records: '23K', status: 'connected', type: 'Database' }
      ]
    },
    {
      id: 7,
      name: '民政局',
      icon: Users,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '345K',
      lastSync: '6分钟前',
      dataTypes: [
        { name: '婚姻登记数据', records: '156K', status: 'connected', type: 'Database' },
        { name: '社会救助数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '养老服务数据', records: '67K', status: 'connected', type: 'API' },
        { name: '社会组织数据', records: '33K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 8,
      name: '司法局',
      icon: Scale,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '78K',
      lastSync: '12分钟前',
      dataTypes: [
        { name: '法律援助数据', records: '34K', status: 'connected', type: 'Database' },
        { name: '公证服务数据', records: '28K', status: 'connected', type: 'API' },
        { name: '人民调解数据', records: '16K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 9,
      name: '财政局',
      icon: CreditCard,
      status: 'connected',
      totalSources: 5,
      activeSources: 5,
      totalRecords: '567K',
      lastSync: '4分钟前',
      dataTypes: [
        { name: '预算执行数据', records: '234K', status: 'connected', type: 'Database' },
        { name: '政府采购数据', records: '156K', status: 'connected', type: 'API' },
        { name: '国有资产数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '财政收支数据', records: '56K', status: 'connected', type: 'Database' },
        { name: '专项资金数据', records: '32K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 10,
      name: '人社局',
      icon: Briefcase,
      status: 'connected',
      totalSources: 6,
      activeSources: 5,
      totalRecords: '1.2M',
      lastSync: '7分钟前',
      dataTypes: [
        { name: '就业登记数据', records: '456K', status: 'connected', type: 'Database' },
        { name: '社保缴费数据', records: '389K', status: 'connected', type: 'Database' },
        { name: '人才信息数据', records: '234K', status: 'warning', type: 'API' },
        { name: '职业培训数据', records: '89K', status: 'connected', type: 'File' },
        { name: '劳动关系数据', records: '23K', status: 'connected', type: 'Database' },
        { name: '工资指导数据', records: '9K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 11,
      name: '自然资源局',
      icon: MapPin,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '678K',
      lastSync: '9分钟前',
      dataTypes: [
        { name: '土地利用数据', records: '345K', status: 'connected', type: 'Database' },
        { name: '不动产登记数据', records: '234K', status: 'connected', type: 'Database' },
        { name: '地理信息数据', records: '67K', status: 'connected', type: 'API' },
        { name: '矿产资源数据', records: '32K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 12,
      name: '生态环境局',
      icon: TreePine,
      status: 'connected',
      totalSources: 5,
      activeSources: 5,
      totalRecords: '234K',
      lastSync: '3分钟前',
      dataTypes: [
        { name: '空气质量数据', records: '89K', status: 'connected', type: 'API' },
        { name: '水质监测数据', records: '67K', status: 'connected', type: 'API' },
        { name: '污染源数据', records: '45K', status: 'connected', type: 'Database' },
        { name: '环评审批数据', records: '23K', status: 'connected', type: 'File' },
        { name: '生态保护数据', records: '10K', status: 'connected', type: 'Database' }
      ]
    },
    // 继续添加剩余的12个单位
    {
      id: 13,
      name: '住建局',
      icon: Home,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '456K',
      lastSync: '11分钟前',
      dataTypes: [
        { name: '房屋建筑数据', records: '234K', status: 'connected', type: 'Database' },
        { name: '城市规划数据', records: '123K', status: 'connected', type: 'API' },
        { name: '建筑许可数据', records: '67K', status: 'connected', type: 'File' },
        { name: '物业管理数据', records: '32K', status: 'connected', type: 'Database' }
      ]
    },
    {
      id: 14,
      name: '交通运输局',
      icon: Car,
      status: 'connected',
      totalSources: 5,
      activeSources: 5,
      totalRecords: '789K',
      lastSync: '2分钟前',
      dataTypes: [
        { name: '道路运输数据', records: '345K', status: 'connected', type: 'Database' },
        { name: '公共交通数据', records: '234K', status: 'connected', type: 'API' },
        { name: '交通基础设施数据', records: '123K', status: 'connected', type: 'Database' },
        { name: '运输企业数据', records: '56K', status: 'connected', type: 'File' },
        { name: '交通违法数据', records: '31K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 15,
      name: '水务局',
      icon: Droplets,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '234K',
      lastSync: '8分钟前',
      dataTypes: [
        { name: '供水管网数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '污水处理数据', records: '67K', status: 'connected', type: 'API' },
        { name: '水资源数据', records: '45K', status: 'connected', type: 'Database' },
        { name: '防汛数据', records: '33K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 16,
      name: '农业农村局',
      icon: Wheat,
      status: 'connected',
      totalSources: 4,
      activeSources: 3,
      totalRecords: '345K',
      lastSync: '15分钟前',
      dataTypes: [
        { name: '农业生产数据', records: '156K', status: 'connected', type: 'Database' },
        { name: '农村土地数据', records: '89K', status: 'connected', type: 'API' },
        { name: '农产品质量数据', records: '67K', status: 'warning', type: 'File' },
        { name: '农业补贴数据', records: '33K', status: 'connected', type: 'Database' }
      ]
    },
    {
      id: 17,
      name: '商务局',
      icon: ShoppingBag,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '123K',
      lastSync: '6分钟前',
      dataTypes: [
        { name: '对外贸易数据', records: '56K', status: 'connected', type: 'Database' },
        { name: '招商引资数据', records: '34K', status: 'connected', type: 'API' },
        { name: '商贸流通数据', records: '33K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 18,
      name: '文旅局',
      icon: Camera,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '89K',
      lastSync: '10分钟前',
      dataTypes: [
        { name: '旅游景区数据', records: '45K', status: 'connected', type: 'Database' },
        { name: '文化场馆数据', records: '28K', status: 'connected', type: 'API' },
        { name: '文物保护数据', records: '16K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 19,
      name: '卫健委',
      icon: Stethoscope,
      status: 'connected',
      totalSources: 6,
      activeSources: 6,
      totalRecords: '1.5M',
      lastSync: '实时',
      dataTypes: [
        { name: '医疗机构数据', records: '456K', status: 'connected', type: 'Database' },
        { name: '医护人员数据', records: '234K', status: 'connected', type: 'Database' },
        { name: '公共卫生数据', records: '345K', status: 'connected', type: 'API' },
        { name: '疫情防控数据', records: '289K', status: 'connected', type: 'Database' },
        { name: '健康档案数据', records: '123K', status: 'connected', type: 'File' },
        { name: '医疗服务数据', records: '53K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 20,
      name: '应急管理局',
      icon: AlertTriangle,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '167K',
      lastSync: '5分钟前',
      dataTypes: [
        { name: '安全生产数据', records: '78K', status: 'connected', type: 'Database' },
        { name: '应急预案数据', records: '45K', status: 'connected', type: 'File' },
        { name: '灾害监测数据', records: '28K', status: 'connected', type: 'API' },
        { name: '应急物资数据', records: '16K', status: 'connected', type: 'Database' }
      ]
    },
    {
      id: 21,
      name: '审计局',
      icon: Eye,
      status: 'connected',
      totalSources: 3,
      activeSources: 3,
      totalRecords: '89K',
      lastSync: '20分钟前',
      dataTypes: [
        { name: '审计项目数据', records: '45K', status: 'connected', type: 'Database' },
        { name: '审计结果数据', records: '28K', status: 'connected', type: 'File' },
        { name: '整改跟踪数据', records: '16K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 22,
      name: '市场监管局',
      icon: Shield,
      status: 'connected',
      totalSources: 5,
      activeSources: 5,
      totalRecords: '567K',
      lastSync: '4分钟前',
      dataTypes: [
        { name: '企业注册数据', records: '234K', status: 'connected', type: 'Database' },
        { name: '食品安全数据', records: '123K', status: 'connected', type: 'API' },
        { name: '产品质量数据', records: '89K', status: 'connected', type: 'Database' },
        { name: '价格监管数据', records: '67K', status: 'connected', type: 'File' },
        { name: '知识产权数据', records: '54K', status: 'connected', type: 'API' }
      ]
    },
    {
      id: 23,
      name: '统计局',
      icon: BarChart3,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '345K',
      lastSync: '7分钟前',
      dataTypes: [
        { name: '经济统计数据', records: '156K', status: 'connected', type: 'Database' },
        { name: '人口统计数据', records: '89K', status: 'connected', type: 'API' },
        { name: '社会统计数据', records: '67K', status: 'connected', type: 'Database' },
        { name: '专项调查数据', records: '33K', status: 'connected', type: 'File' }
      ]
    },
    {
      id: 24,
      name: '医保局',
      icon: Heart,
      status: 'connected',
      totalSources: 4,
      activeSources: 4,
      totalRecords: '678K',
      lastSync: '3分钟前',
      dataTypes: [
        { name: '医保参保数据', records: '345K', status: 'connected', type: 'Database' },
        { name: '医保结算数据', records: '234K', status: 'connected', type: 'API' },
        { name: '定点医疗机构数据', records: '67K', status: 'connected', type: 'Database' },
        { name: '药品目录数据', records: '32K', status: 'connected', type: 'File' }
      ]
    }
  ]

  // 总体统计数据
  const overallStats = [
    { label: '数据来源单位', value: '24', trend: '全覆盖', icon: Building2, color: 'purple' },
    { label: '数据源总数', value: '156', trend: '+12', icon: Database, color: 'blue' },
    { label: '在线数据源', value: '142', trend: '91%', icon: Wifi, color: 'green' },
    { label: '今日采集量', value: '2.8TB', trend: '+15%', icon: Activity, color: 'orange' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'syncing': return <Clock className="w-4 h-4 text-blue-500" />
      default: return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '正常运行'
      case 'error': return '连接异常'
      case 'warning': return '部分异常'
      case 'syncing': return '同步中'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-700 border-green-200'
      case 'error': return 'bg-red-100 text-red-700 border-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'syncing': return 'bg-blue-100 text-blue-700 border-blue-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getDataTypeIcon = (type: string) => {
    switch (type) {
      case 'Database': return <Database className="w-4 h-4" />
      case 'API': return <Globe className="w-4 h-4" />
      case 'File': return <FileText className="w-4 h-4" />
      default: return <Server className="w-4 h-4" />
    }
  }

  return (
    <div className="w-full px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">全市数据采集概览</h1>
        <p className="text-xl text-gray-600">24个数据来源单位的数据采集情况实时监控</p>
      </div>

      {/* 总体统计 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
        {overallStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 搜索和过滤控件 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 border border-white/20">
        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between space-y-4 xl:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 flex-1">
            {/* 搜索框 */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索数据来源单位..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm w-full"
              />
            </div>

            {/* 状态过滤 */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="all">全部状态</option>
                <option value="connected">正常运行</option>
                <option value="warning">部分异常</option>
                <option value="error">连接异常</option>
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between xl:justify-end xl:space-x-6">
            {/* 视图切换 */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('compact')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'compact'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            <div className="text-sm text-gray-600 whitespace-nowrap">
              最后更新: {new Date().toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <button className="bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加数据源</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Zap className="w-5 h-5" />
            <span>批量同步</span>
          </button>
        </div>
      </div>

      {/* 过滤结果信息 */}
      {(() => {
        const filteredUnits = dataSourceUnits.filter(unit => {
          const matchesSearch = unit.name.toLowerCase().includes(searchTerm.toLowerCase())
          const matchesStatus = statusFilter === 'all' || unit.status === statusFilter
          return matchesSearch && matchesStatus
        })

        return (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm text-gray-600">
                显示 {filteredUnits.length} / {dataSourceUnits.length} 个数据来源单位
                {searchTerm && ` · 搜索: "${searchTerm}"`}
                {statusFilter !== 'all' && ` · 状态: ${getStatusText(statusFilter)}`}
              </div>
              <div className="text-xs text-gray-500">
                视图模式: {viewMode === 'compact' ? '紧凑' : '详细'}
              </div>
            </div>

            {/* 数据来源单位列表 */}
            <div className={`grid gap-4 ${
              viewMode === 'compact'
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6'
                : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'
            }`}>
              {filteredUnits.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <div className="text-gray-400 mb-2">
                    <Database className="w-12 h-12 mx-auto mb-4" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的数据来源单位</h3>
                  <p className="text-gray-600">请尝试调整搜索条件或过滤器</p>
                </div>
              ) : (
                filteredUnits.map((unit) => {
          const Icon = unit.icon
          return viewMode === 'compact' ? (
            // 紧凑视图模式
            <div
              key={unit.id}
              className="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/20 hover:shadow-md transition-all duration-300 group"
            >
              <div className="p-2.5">
                {/* 单位头部信息 - 紧凑 */}
                <div className="flex items-center justify-between mb-1.5">
                  <div className="flex items-center space-x-1.5">
                    <div className="w-5 h-5 bg-gradient-to-br from-purple-500 to-violet-500 rounded flex items-center justify-center flex-shrink-0">
                      <Icon className="w-2.5 h-2.5 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-xs font-bold text-gray-900 truncate leading-tight">{unit.name}</h3>
                      <div className="flex items-center space-x-1 mt-0.5">
                        <span className={`px-1 py-0.5 rounded text-xs font-medium border ${getStatusColor(unit.status)}`}>
                          {getStatusText(unit.status)}
                        </span>
                        {getStatusIcon(unit.status)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 统计信息 - 超紧凑 */}
                <div className="grid grid-cols-3 gap-1 mb-1.5 text-center">
                  <div>
                    <p className="text-xs text-gray-600 leading-tight">总数</p>
                    <p className="text-sm font-bold text-gray-900 leading-tight">{unit.totalSources}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 leading-tight">在线</p>
                    <p className="text-sm font-bold text-green-600 leading-tight">{unit.activeSources}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-600 leading-tight">数据量</p>
                    <p className="text-sm font-bold text-blue-600 leading-tight">{unit.totalRecords}</p>
                  </div>
                </div>

                {/* 数据类型数量 */}
                <div className="text-center py-1 bg-gray-50/50 rounded text-xs text-gray-600 mb-1.5">
                  {unit.dataTypes.length} 种数据类型
                </div>

                {/* 操作按钮 - 紧凑 */}
                <div className="flex items-center justify-center space-x-2">
                  <button className="text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors">
                    详情
                  </button>
                  <Link
                    href={`/collection/unit/${unit.id}`}
                    className="text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors"
                  >
                    管理
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            // 标准视图模式
            <div
              key={unit.id}
              className="bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300 group"
            >
              <div className="p-4">
                {/* 单位头部信息 */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-sm font-bold text-gray-900 truncate">{unit.name}</h3>
                      <div className="flex items-center space-x-1 mt-1">
                        <span className={`px-1.5 py-0.5 rounded text-xs font-medium border ${getStatusColor(unit.status)}`}>
                          {getStatusText(unit.status)}
                        </span>
                        {getStatusIcon(unit.status)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-xs text-gray-600">
                    {unit.lastSync}
                  </div>
                </div>

                {/* 单位统计信息 */}
                <div className="grid grid-cols-3 gap-2 mb-3 p-2 bg-gray-50/50 rounded-lg">
                  <div className="text-center">
                    <p className="text-xs text-gray-600">总数</p>
                    <p className="text-sm font-bold text-gray-900">{unit.totalSources}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-600">在线</p>
                    <p className="text-sm font-bold text-green-600">{unit.activeSources}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-600">数据量</p>
                    <p className="text-sm font-bold text-blue-600">{unit.totalRecords}</p>
                  </div>
                </div>

                {/* 数据类型详情 */}
                <div className="space-y-1.5">
                  <h4 className="text-xs font-semibold text-gray-700">数据类型 ({unit.dataTypes.length})</h4>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {unit.dataTypes.map((dataType, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 bg-white/50 rounded border border-gray-100">
                        <div className="flex items-center space-x-2 min-w-0 flex-1">
                          <div className="flex items-center space-x-1 flex-shrink-0">
                            {getDataTypeIcon(dataType.type)}
                            <span className="text-xs text-gray-500">{dataType.type}</span>
                          </div>
                          <span className="text-xs font-medium text-gray-900 truncate">{dataType.name}</span>
                          {getStatusIcon(dataType.status)}
                        </div>
                        <div className="text-right flex-shrink-0 ml-2">
                          <p className="text-xs font-semibold text-gray-900">{dataType.records}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                  <div className="flex items-center space-x-2">
                    <button className="text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors">
                      详情
                    </button>
                    <button className="text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors">
                      同步
                    </button>
                  </div>
                  <Link
                    href={`/collection/unit/${unit.id}`}
                    className="text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-xs"
                  >
                    <span>管理</span>
                    <ArrowRight className="w-3 h-3" />
                  </Link>
                </div>
              </div>
            </div>
                  )
                })
              )}
            </div>
          </>
        )
      })()}
    </div>
  )
}
