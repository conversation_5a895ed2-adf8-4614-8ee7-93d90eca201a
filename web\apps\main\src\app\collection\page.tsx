'use client'

import Link from 'next/link'
import { 
  Database, 
  Server, 
  Cloud, 
  FileText, 
  Wifi,
  CheckCircle,
  AlertCircle,
  Clock,
  Plus,
  ArrowRight,
  Activity,
  HardDrive,
  Zap
} from 'lucide-react'

export default function CollectionSystemPage() {
  const dataSources = [
    {
      id: 1,
      name: '公安局人口数据库',
      type: 'MySQL',
      status: 'connected',
      lastSync: '2分钟前',
      records: '2.4M',
      department: '公安局',
      frequency: '实时'
    },
    {
      id: 2,
      name: '民政局婚姻登记系统',
      type: 'Oracle',
      status: 'connected',
      lastSync: '5分钟前',
      records: '156K',
      department: '民政局',
      frequency: '每小时'
    },
    {
      id: 3,
      name: '教育局学籍管理系统',
      type: 'PostgreSQL',
      status: 'error',
      lastSync: '2小时前',
      records: '89K',
      department: '教育局',
      frequency: '每日'
    },
    {
      id: 4,
      name: '卫健委医疗数据平台',
      type: 'API',
      status: 'syncing',
      lastSync: '刚刚',
      records: '1.2M',
      department: '卫健委',
      frequency: '实时'
    }
  ]

  const quickStats = [
    { label: '数据源总数', value: '24', trend: '+3', icon: Database, color: 'purple' },
    { label: '在线数据源', value: '21', trend: '+1', icon: Wifi, color: 'green' },
    { label: '今日采集量', value: '156K', trend: '+12%', icon: Activity, color: 'blue' },
    { label: '存储容量', value: '2.4TB', trend: '+8%', icon: HardDrive, color: 'orange' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error': return <AlertCircle className="w-5 h-5 text-red-500" />
      case 'syncing': return <Clock className="w-5 h-5 text-blue-500" />
      default: return <AlertCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '已连接'
      case 'error': return '连接错误'
      case 'syncing': return '同步中'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-700'
      case 'error': return 'bg-red-100 text-red-700'
      case 'syncing': return 'bg-blue-100 text-blue-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">数据采集管理中心</h1>
        <p className="text-xl text-gray-600">管理和监控您的数据源连接与采集任务</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加数据源</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Zap className="w-5 h-5" />
            <span>批量同步</span>
          </button>
        </div>
      </div>

      {/* 数据源列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {dataSources.map((source, index) => (
          <div
            key={source.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{source.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(source.status)}`}>
                      {getStatusText(source.status)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span className="bg-gray-100 px-2 py-1 rounded-full">{source.type}</span>
                    <span>{source.department}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(source.status)}
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Database className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">数据量</p>
                  <p className="text-lg font-bold text-gray-900">{source.records}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">同步频率</p>
                  <p className="text-sm font-medium text-gray-900">{source.frequency}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">最后同步</p>
                  <p className="text-sm font-medium text-gray-900">{source.lastSync}</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="text-purple-600 hover:text-purple-700 font-medium text-sm transition-colors">
                    测试连接
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    立即同步
                  </button>
                </div>
                <Link
                  href={`/collection/source/${source.id}`}
                  className="text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>管理</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
