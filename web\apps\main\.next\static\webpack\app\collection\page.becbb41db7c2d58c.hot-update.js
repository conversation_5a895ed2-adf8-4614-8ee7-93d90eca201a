"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/page",{

/***/ "(app-pages-browser)/./src/app/collection/page.tsx":
/*!*************************************!*\
  !*** ./src/app/collection/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tree-pine.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wheat.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CollectionSystemPage() {\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"compact\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"all\");\n    // 全市24个数据来源单位及其采集的数据类型\n    const dataSourceUnits = [\n        {\n            id: 1,\n            name: \"市政府办公厅\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"45K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"政务公开数据\",\n                    records: \"15K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"会议纪要数据\",\n                    records: \"20K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公文流转数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            name: \"发展改革委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"128K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"项目投资数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"经济指标数据\",\n                    records: \"42K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"规划数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"价格监测数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            name: \"教育局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            status: \"warning\",\n            totalSources: 5,\n            activeSources: 4,\n            totalRecords: \"892K\",\n            lastSync: \"2小时前\",\n            dataTypes: [\n                {\n                    name: \"学生学籍数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"教师信息数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"学校基础数据\",\n                    records: \"234K\",\n                    status: \"error\",\n                    type: \"API\"\n                },\n                {\n                    name: \"考试成绩数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"教育资源数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            name: \"科技局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"67K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"科技项目数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专利申请数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"科技企业数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            name: \"工信局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"1分钟前\",\n            dataTypes: [\n                {\n                    name: \"工业企业数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"产业园区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"信息化项目数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"节能减排数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            name: \"公安局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"2.4M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"人口基础信息\",\n                    records: \"1.2M\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"户籍管理数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"身份证数据\",\n                    records: \"567K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"治安管理数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"案件管理数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 7,\n            name: \"民政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"婚姻登记数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社会救助数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"养老服务数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会组织数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 8,\n            name: \"司法局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"78K\",\n            lastSync: \"12分钟前\",\n            dataTypes: [\n                {\n                    name: \"法律援助数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公证服务数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"人民调解数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 9,\n            name: \"财政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"预算执行数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"政府采购数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"国有资产数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"财政收支数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项资金数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 10,\n            name: \"人社局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 5,\n            totalRecords: \"1.2M\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"就业登记数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社保缴费数据\",\n                    records: \"389K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人才信息数据\",\n                    records: \"234K\",\n                    status: \"warning\",\n                    type: \"API\"\n                },\n                {\n                    name: \"职业培训数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"劳动关系数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"工资指导数据\",\n                    records: \"9K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 11,\n            name: \"自然资源局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"9分钟前\",\n            dataTypes: [\n                {\n                    name: \"土地利用数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"不动产登记数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"地理信息数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"矿产资源数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 12,\n            name: \"生态环境局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"234K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"空气质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水质监测数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"污染源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"环评审批数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"生态保护数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        // 继续添加剩余的12个单位\n        {\n            id: 13,\n            name: \"住建局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"456K\",\n            lastSync: \"11分钟前\",\n            dataTypes: [\n                {\n                    name: \"房屋建筑数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"城市规划数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"建筑许可数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"物业管理数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 14,\n            name: \"交通运输局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"789K\",\n            lastSync: \"2分钟前\",\n            dataTypes: [\n                {\n                    name: \"道路运输数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共交通数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通基础设施数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"运输企业数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"31K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 15,\n            name: \"水务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"供水管网数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"污水处理数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水资源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"防汛数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 16,\n            name: \"农业农村局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 3,\n            totalRecords: \"345K\",\n            lastSync: \"15分钟前\",\n            dataTypes: [\n                {\n                    name: \"农业生产数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"农村土地数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"农产品质量数据\",\n                    records: \"67K\",\n                    status: \"warning\",\n                    type: \"File\"\n                },\n                {\n                    name: \"农业补贴数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 17,\n            name: \"商务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"123K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"对外贸易数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"招商引资数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"商贸流通数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 18,\n            name: \"文旅局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"10分钟前\",\n            dataTypes: [\n                {\n                    name: \"旅游景区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"文化场馆数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"文物保护数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 19,\n            name: \"卫健委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"1.5M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"医疗机构数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医护人员数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共卫生数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"疫情防控数据\",\n                    records: \"289K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"健康档案数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"医疗服务数据\",\n                    records: \"53K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 20,\n            name: \"应急管理局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"167K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"安全生产数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"应急预案数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"灾害监测数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"应急物资数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 21,\n            name: \"审计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"20分钟前\",\n            dataTypes: [\n                {\n                    name: \"审计项目数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"审计结果数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"整改跟踪数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 22,\n            name: \"市场监管局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"企业注册数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"食品安全数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"产品质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"价格监管数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"知识产权数据\",\n                    records: \"54K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 23,\n            name: \"统计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"经济统计数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人口统计数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会统计数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项调查数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 24,\n            name: \"医保局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"医保参保数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医保结算数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"定点医疗机构数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"药品目录数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        }\n    ];\n    // 总体统计数据\n    const overallStats = [\n        {\n            label: \"数据来源单位\",\n            value: \"24\",\n            trend: \"全覆盖\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            label: \"数据源总数\",\n            value: \"156\",\n            trend: \"+12\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"在线数据源\",\n            value: \"142\",\n            trend: \"91%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"今日采集量\",\n            value: \"2.8TB\",\n            trend: \"+15%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            color: \"orange\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 32\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 28\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 30\n                }, this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"正常运行\";\n            case \"error\":\n                return \"连接异常\";\n            case \"warning\":\n                return \"部分异常\";\n            case \"syncing\":\n                return \"同步中\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"error\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            case \"syncing\":\n                return \"bg-blue-100 text-blue-700 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    const getDataTypeIcon = (type)=>{\n        switch(type){\n            case \"Database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 31\n                }, this);\n            case \"API\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 26\n                }, this);\n            case \"File\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 mb-6\",\n                children: overallStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/20 hover:shadow-lg transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-w-0 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-medium text-gray-600 mb-1 truncate\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-0.5\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-\".concat(stat.color, \"-500 to-\").concat(stat.color, \"-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-2\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-sm rounded-xl p-3 mb-4 border border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0 lg:space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1 max-w-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                            className: \"absolute left-2.5 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索数据来源单位...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-9 pr-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"border border-gray-200 rounded-lg px-2.5 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"全部状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"connected\",\n                                                    children: \"正常运行\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"warning\",\n                                                    children: \"部分异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"error\",\n                                                    children: \"连接异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between xl:justify-end xl:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 bg-gray-100 rounded-lg p-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"compact\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"compact\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"grid\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 whitespace-nowrap\",\n                                    children: [\n                                        \"最后更新: \",\n                                        new Date().toLocaleString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"添加数据源\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"批量同步\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 584,\n                columnNumber: 7\n            }, this),\n            (()=>{\n                const filteredUnits = dataSourceUnits.filter((unit)=>{\n                    const matchesSearch = unit.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesStatus = statusFilter === \"all\" || unit.status === statusFilter;\n                    return matchesSearch && matchesStatus;\n                });\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"显示 \",\n                                        filteredUnits.length,\n                                        \" / \",\n                                        dataSourceUnits.length,\n                                        \" 个数据来源单位\",\n                                        searchTerm && ' \\xb7 搜索: \"'.concat(searchTerm, '\"'),\n                                        statusFilter !== \"all\" && \" \\xb7 状态: \".concat(getStatusText(statusFilter))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"视图模式: \",\n                                        viewMode === \"compact\" ? \"紧凑\" : \"详细\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 \".concat(viewMode === \"compact\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6\" : \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5\"),\n                            children: filteredUnits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-full text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"w-12 h-12 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"未找到匹配的数据来源单位\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"请尝试调整搜索条件或过滤器\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 17\n                            }, this) : filteredUnits.map((unit)=>{\n                                const Icon = unit.icon;\n                                return viewMode === \"compact\" ? // 紧凑视图模式\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/20 hover:shadow-md transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-1.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1.5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gradient-to-br from-purple-500 to-violet-500 rounded flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-2.5 h-2.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"min-w-0 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xs font-bold text-gray-900 truncate leading-tight\",\n                                                                    children: unit.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-0.5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                            children: getStatusText(unit.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(unit.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-1 mb-1.5 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 leading-tight\",\n                                                                children: \"总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900 leading-tight\",\n                                                                children: unit.totalSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 leading-tight\",\n                                                                children: \"在线\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-green-600 leading-tight\",\n                                                                children: unit.activeSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 leading-tight\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-blue-600 leading-tight\",\n                                                                children: unit.totalRecords\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-1 bg-gray-50/50 rounded text-xs text-gray-600 mb-1.5\",\n                                                children: [\n                                                    unit.dataTypes.length,\n                                                    \" 种数据类型\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                        children: \"详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/collection/unit/\".concat(unit.id),\n                                                        className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                        children: \"管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                }, unit.id, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 13\n                                }, this) : // 标准视图模式\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0 flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                        children: unit.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-1.5 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                                children: getStatusText(unit.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            getStatusIcon(unit.status)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right text-xs text-gray-600\",\n                                                        children: unit.lastSync\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-2 mb-3 p-2 bg-gray-50/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900\",\n                                                                children: unit.totalSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"在线\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-green-600\",\n                                                                children: unit.activeSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-blue-600\",\n                                                                children: unit.totalRecords\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xs font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"数据类型 (\",\n                                                            unit.dataTypes.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-32 overflow-y-auto space-y-1\",\n                                                        children: unit.dataTypes.map((dataType, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-2 bg-white/50 rounded border border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 min-w-0 flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1 flex-shrink-0\",\n                                                                                children: [\n                                                                                    getDataTypeIcon(dataType.type),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: dataType.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                        lineNumber: 748,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                                children: dataType.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            getStatusIcon(dataType.status)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right flex-shrink-0 ml-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold text-gray-900\",\n                                                                            children: dataType.records\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 744,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                                children: \"详情\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                                children: \"同步\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/collection/unit/\".concat(unit.id),\n                                                        className: \"text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, this)\n                                }, unit.id, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true);\n            })()\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemPage, \"IGmnZS9Y1IFhVtN0SFrVv9COoy8=\");\n_c = CollectionSystemPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/page.tsx\n"));

/***/ })

});