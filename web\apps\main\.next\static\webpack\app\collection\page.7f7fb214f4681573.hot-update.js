"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/page",{

/***/ "(app-pages-browser)/./src/app/collection/page.tsx":
/*!*************************************!*\
  !*** ./src/app/collection/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tree-pine.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wheat.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CollectionSystemPage() {\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"compact\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"all\");\n    // 全市24个数据来源单位及其采集的数据类型\n    const dataSourceUnits = [\n        {\n            id: 1,\n            name: \"市政府办公厅\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"45K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"政务公开数据\",\n                    records: \"15K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"会议纪要数据\",\n                    records: \"20K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公文流转数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            name: \"发展改革委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"128K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"项目投资数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"经济指标数据\",\n                    records: \"42K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"规划数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"价格监测数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            name: \"教育局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            status: \"warning\",\n            totalSources: 5,\n            activeSources: 4,\n            totalRecords: \"892K\",\n            lastSync: \"2小时前\",\n            dataTypes: [\n                {\n                    name: \"学生学籍数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"教师信息数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"学校基础数据\",\n                    records: \"234K\",\n                    status: \"error\",\n                    type: \"API\"\n                },\n                {\n                    name: \"考试成绩数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"教育资源数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            name: \"科技局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"67K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"科技项目数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专利申请数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"科技企业数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            name: \"工信局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"1分钟前\",\n            dataTypes: [\n                {\n                    name: \"工业企业数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"产业园区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"信息化项目数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"节能减排数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            name: \"公安局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"2.4M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"人口基础信息\",\n                    records: \"1.2M\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"户籍管理数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"身份证数据\",\n                    records: \"567K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"治安管理数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"案件管理数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 7,\n            name: \"民政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"婚姻登记数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社会救助数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"养老服务数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会组织数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 8,\n            name: \"司法局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"78K\",\n            lastSync: \"12分钟前\",\n            dataTypes: [\n                {\n                    name: \"法律援助数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公证服务数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"人民调解数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 9,\n            name: \"财政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"预算执行数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"政府采购数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"国有资产数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"财政收支数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项资金数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 10,\n            name: \"人社局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 5,\n            totalRecords: \"1.2M\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"就业登记数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社保缴费数据\",\n                    records: \"389K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人才信息数据\",\n                    records: \"234K\",\n                    status: \"warning\",\n                    type: \"API\"\n                },\n                {\n                    name: \"职业培训数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"劳动关系数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"工资指导数据\",\n                    records: \"9K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 11,\n            name: \"自然资源局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"9分钟前\",\n            dataTypes: [\n                {\n                    name: \"土地利用数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"不动产登记数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"地理信息数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"矿产资源数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 12,\n            name: \"生态环境局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"234K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"空气质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水质监测数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"污染源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"环评审批数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"生态保护数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        // 继续添加剩余的12个单位\n        {\n            id: 13,\n            name: \"住建局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"456K\",\n            lastSync: \"11分钟前\",\n            dataTypes: [\n                {\n                    name: \"房屋建筑数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"城市规划数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"建筑许可数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"物业管理数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 14,\n            name: \"交通运输局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"789K\",\n            lastSync: \"2分钟前\",\n            dataTypes: [\n                {\n                    name: \"道路运输数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共交通数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通基础设施数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"运输企业数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"31K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 15,\n            name: \"水务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"供水管网数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"污水处理数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水资源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"防汛数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 16,\n            name: \"农业农村局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 3,\n            totalRecords: \"345K\",\n            lastSync: \"15分钟前\",\n            dataTypes: [\n                {\n                    name: \"农业生产数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"农村土地数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"农产品质量数据\",\n                    records: \"67K\",\n                    status: \"warning\",\n                    type: \"File\"\n                },\n                {\n                    name: \"农业补贴数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 17,\n            name: \"商务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"123K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"对外贸易数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"招商引资数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"商贸流通数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 18,\n            name: \"文旅局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"10分钟前\",\n            dataTypes: [\n                {\n                    name: \"旅游景区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"文化场馆数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"文物保护数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 19,\n            name: \"卫健委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"1.5M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"医疗机构数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医护人员数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共卫生数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"疫情防控数据\",\n                    records: \"289K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"健康档案数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"医疗服务数据\",\n                    records: \"53K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 20,\n            name: \"应急管理局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"167K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"安全生产数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"应急预案数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"灾害监测数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"应急物资数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 21,\n            name: \"审计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"20分钟前\",\n            dataTypes: [\n                {\n                    name: \"审计项目数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"审计结果数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"整改跟踪数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 22,\n            name: \"市场监管局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"企业注册数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"食品安全数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"产品质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"价格监管数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"知识产权数据\",\n                    records: \"54K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 23,\n            name: \"统计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"经济统计数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人口统计数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会统计数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项调查数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 24,\n            name: \"医保局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"医保参保数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医保结算数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"定点医疗机构数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"药品目录数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        }\n    ];\n    // 总体统计数据\n    const overallStats = [\n        {\n            label: \"数据来源单位\",\n            value: \"24\",\n            trend: \"全覆盖\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            label: \"数据源总数\",\n            value: \"156\",\n            trend: \"+12\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"在线数据源\",\n            value: \"142\",\n            trend: \"91%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"今日采集量\",\n            value: \"2.8TB\",\n            trend: \"+15%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            color: \"orange\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 32\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 28\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 30\n                }, this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"正常运行\";\n            case \"error\":\n                return \"连接异常\";\n            case \"warning\":\n                return \"部分异常\";\n            case \"syncing\":\n                return \"同步中\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"error\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            case \"syncing\":\n                return \"bg-blue-100 text-blue-700 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    const getDataTypeIcon = (type)=>{\n        switch(type){\n            case \"Database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 31\n                }, this);\n            case \"API\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 26\n                }, this);\n            case \"File\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"全市数据采集概览\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"24个数据来源单位的数据采集情况实时监控\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: overallStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-\".concat(stat.color, \"-500 to-\").concat(stat.color, \"-600 rounded-xl flex items-center justify-center\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 border border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索数据来源单位...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm w-full sm:w-64\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"全部状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"connected\",\n                                                    children: \"正常运行\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"warning\",\n                                                    children: \"部分异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"error\",\n                                                    children: \"连接异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 bg-gray-100 rounded-lg p-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"compact\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"compact\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"grid\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 ml-4\",\n                                    children: [\n                                        \"最后更新: \",\n                                        new Date().toLocaleString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"添加数据源\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"批量同步\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 590,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 \".concat(viewMode === \"compact\" ? \"grid-cols-1 lg:grid-cols-2 xl:grid-cols-4\" : \"grid-cols-1 lg:grid-cols-2 xl:grid-cols-3\"),\n                children: dataSourceUnits.filter((unit)=>{\n                    const matchesSearch = unit.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesStatus = statusFilter === \"all\" || unit.status === statusFilter;\n                    return matchesSearch && matchesStatus;\n                }).map((unit)=>{\n                    const Icon = unit.icon;\n                    return viewMode === \"compact\" ? // 紧凑视图模式\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/20 hover:shadow-md transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-gradient-to-br from-purple-500 to-violet-500 rounded flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"w-3 h-3 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xs font-bold text-gray-900 truncate\",\n                                                        children: unit.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-1 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                children: getStatusText(unit.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            getStatusIcon(unit.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-1 mb-2 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"总数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-gray-900\",\n                                                    children: unit.totalSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"在线\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-green-600\",\n                                                    children: unit.activeSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"数据量\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-blue-600\",\n                                                    children: unit.totalRecords\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-1 bg-gray-50/50 rounded text-xs text-gray-600 mb-2\",\n                                    children: [\n                                        unit.dataTypes.length,\n                                        \" 种数据类型\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                            children: \"详情\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/collection/unit/\".concat(unit.id),\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                            children: \"管理\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 15\n                        }, this)\n                    }, unit.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 13\n                    }, this) : // 标准视图模式\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate\",\n                                                            children: unit.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-1.5 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                    children: getStatusText(unit.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getStatusIcon(unit.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-xs text-gray-600\",\n                                            children: unit.lastSync\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2 mb-3 p-2 bg-gray-50/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"总数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-gray-900\",\n                                                    children: unit.totalSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"在线\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-green-600\",\n                                                    children: unit.activeSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"数据量\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-blue-600\",\n                                                    children: unit.totalRecords\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xs font-semibold text-gray-700\",\n                                            children: [\n                                                \"数据类型 (\",\n                                                unit.dataTypes.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-32 overflow-y-auto space-y-1\",\n                                            children: unit.dataTypes.map((dataType, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-white/50 rounded border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 min-w-0 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 flex-shrink-0\",\n                                                                    children: [\n                                                                        getDataTypeIcon(dataType.type),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: dataType.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                    children: dataType.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                getStatusIcon(dataType.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right flex-shrink-0 ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-semibold text-gray-900\",\n                                                                children: dataType.records\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                    children: \"详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                    children: \"同步\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 749,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/collection/unit/\".concat(unit.id),\n                                            className: \"text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 15\n                        }, this)\n                    }, unit.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 604,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemPage, \"IGmnZS9Y1IFhVtN0SFrVv9COoy8=\");\n_c = CollectionSystemPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/page.tsx\n"));

/***/ })

});