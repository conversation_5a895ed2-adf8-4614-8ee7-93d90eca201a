'use client'

import Link from 'next/link'
import { 
  Monitor, 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  Activity,
  Eye,
  Settings,
  Plus,
  ArrowRight,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'

export default function ScreenSystemPage() {
  const dashboards = [
    {
      id: 1,
      name: '政务数据总览',
      description: '全市政务数据实时监控大屏',
      thumbnail: '/api/placeholder/400/300',
      status: 'active',
      viewers: 156,
      lastUpdated: '2分钟前',
      category: '综合监控'
    },
    {
      id: 2,
      name: '人口统计分析',
      description: '人口数据多维度分析展示',
      thumbnail: '/api/placeholder/400/300',
      status: 'active',
      viewers: 89,
      lastUpdated: '5分钟前',
      category: '人口统计'
    },
    {
      id: 3,
      name: '经济运行监控',
      description: '经济指标实时监控与预警',
      thumbnail: '/api/placeholder/400/300',
      status: 'maintenance',
      viewers: 234,
      lastUpdated: '1小时前',
      category: '经济分析'
    },
    {
      id: 4,
      name: '环境质量监测',
      description: '环境数据实时监测大屏',
      thumbnail: '/api/placeholder/400/300',
      status: 'active',
      viewers: 67,
      lastUpdated: '刚刚',
      category: '环境监测'
    }
  ]

  const quickStats = [
    { label: '活跃大屏', value: '12', trend: '+2', icon: Monitor, color: 'blue' },
    { label: '总访问量', value: '2.4K', trend: '+15%', icon: Eye, color: 'green' },
    { label: '数据源', value: '24', trend: '+3', icon: BarChart3, color: 'purple' },
    { label: '实时更新', value: '98%', trend: '+1%', icon: Activity, color: 'orange' }
  ]

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">数据大屏管理中心</h1>
        <p className="text-xl text-gray-600">创建、管理和监控您的数据可视化大屏</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>创建大屏</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Settings className="w-5 h-5" />
            <span>模板管理</span>
          </button>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-600 hover:text-blue-600 transition-colors">
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 大屏列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {dashboards.map((dashboard, index) => (
          <div
            key={dashboard.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group overflow-hidden"
          >
            {/* 缩略图 */}
            <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Monitor className="w-16 h-16 text-gray-400" />
              </div>
              
              {/* 状态指示器 */}
              <div className="absolute top-4 left-4">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  dashboard.status === 'active' 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-yellow-100 text-yellow-700'
                }`}>
                  {dashboard.status === 'active' ? '运行中' : '维护中'}
                </span>
              </div>

              {/* 播放按钮 */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                <button className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-75 group-hover:scale-100">
                  <Play className="w-8 h-8 text-blue-600 ml-1" />
                </button>
              </div>
            </div>

            {/* 内容 */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-1">{dashboard.name}</h3>
                  <p className="text-sm text-gray-600">{dashboard.description}</p>
                </div>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span className="bg-gray-100 px-2 py-1 rounded-full">{dashboard.category}</span>
                <span>{dashboard.lastUpdated}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Eye className="w-4 h-4" />
                  <span>{dashboard.viewers} 观看中</span>
                </div>
                <Link
                  href={`/screen/view/${dashboard.id}`}
                  className="text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1 transition-colors"
                >
                  <span>查看</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}

        {/* 创建新大屏卡片 */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border-2 border-dashed border-gray-300 hover:border-blue-400 transition-all duration-300 group cursor-pointer">
          <div className="h-full flex flex-col items-center justify-center p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
              <Plus className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">创建新大屏</h3>
            <p className="text-sm text-gray-600">选择模板或从头开始创建</p>
          </div>
        </div>
      </div>
    </div>
  )
}
