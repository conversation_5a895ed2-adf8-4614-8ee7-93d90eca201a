"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HomePage() {\n    _s();\n    const [showModuleOverlay, setShowModuleOverlay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 业务模块数据\n    const businessModules = [\n        {\n            name: \"数据大屏\",\n            description: \"实时数据可视化展示，支持多种图表和指标监控\",\n            href: \"/screen\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            gradient: \"from-blue-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDCCA\",\n            features: [\n                \"实时监控\",\n                \"可视化图表\",\n                \"大屏展示\"\n            ]\n        },\n        {\n            name: \"报表分析\",\n            description: \"多维度数据分析报表，支持自定义报表生成\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            gradient: \"from-green-500 to-emerald-500\",\n            logo: \"\\uD83D\\uDCC8\",\n            features: [\n                \"多维分析\",\n                \"自定义报表\",\n                \"数据导出\"\n            ]\n        },\n        {\n            name: \"数据采集\",\n            description: \"多源数据采集接入，支持实时和批量数据导入\",\n            href: \"/collection\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            gradient: \"from-purple-500 to-violet-500\",\n            logo: \"\\uD83D\\uDD04\",\n            features: [\n                \"多源接入\",\n                \"实时采集\",\n                \"数据清洗\"\n            ]\n        },\n        {\n            name: \"数据汇聚\",\n            description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n            href: \"/aggregation\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            gradient: \"from-orange-500 to-amber-500\",\n            logo: \"\\uD83D\\uDD17\",\n            features: [\n                \"数据整合\",\n                \"标准化\",\n                \"质量控制\"\n            ]\n        },\n        {\n            name: \"数据治理\",\n            description: \"数据质量管控，数据清洗、去重、标准化处理\",\n            href: \"/governance\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            gradient: \"from-red-500 to-rose-500\",\n            logo: \"\\uD83D\\uDEE1️\",\n            features: [\n                \"质量管控\",\n                \"数据清洗\",\n                \"合规管理\"\n            ]\n        },\n        {\n            name: \"资源池管理\",\n            description: \"数据资源统一管理，资源目录和权限控制\",\n            href: \"/resources\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            gradient: \"from-indigo-500 to-blue-500\",\n            logo: \"\\uD83D\\uDCBE\",\n            features: [\n                \"资源目录\",\n                \"权限管理\",\n                \"存储优化\"\n            ]\n        },\n        {\n            name: \"设备监控\",\n            description: \"服务器设备实时监控，性能指标和告警管理\",\n            href: \"/monitoring\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            gradient: \"from-teal-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDDA5️\",\n            features: [\n                \"实时监控\",\n                \"性能分析\",\n                \"告警管理\"\n            ]\n        },\n        {\n            name: \"网络管理\",\n            description: \"多物理网络管理，网络拓扑和流量监控\",\n            href: \"/network\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            gradient: \"from-pink-500 to-rose-500\",\n            logo: \"\\uD83C\\uDF10\",\n            features: [\n                \"网络拓扑\",\n                \"流量监控\",\n                \"安全管理\"\n            ]\n        }\n    ];\n    // 核心能力\n    const capabilities = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"数据全生命周期管理\",\n            description: \"从数据采集、清洗、存储到应用的全流程管理，确保数据质量和安全性\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"智能数据分析\",\n            description: \"多维度数据分析和可视化展示，支持实时监控和预测分析\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"数据安全治理\",\n            description: \"完善的数据安全体系和质量管控机制，保障数据合规使用\",\n            gradient: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"跨部门数据共享\",\n            description: \"打破数据孤岛，实现政务数据统一共享和协同应用\",\n            gradient: \"from-orange-500 to-red-500\"\n        }\n    ];\n    // 解决的场景\n    const scenarios = [\n        {\n            title: \"政府决策支撑\",\n            description: \"为领导层提供实时、准确的数据分析，支撑科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            title: \"跨部门协同\",\n            description: \"消除信息孤岛，实现部门间数据无缝对接和业务协同\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            title: \"公共服务优化\",\n            description: \"通过数据分析优化公共服务流程，提升市民满意度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            title: \"监管效能提升\",\n            description: \"利用大数据技术提升监管效率和精准度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    // 数据来源单位\n    const dataSourceUnits = [\n        \"市政府办公厅\",\n        \"发展改革委\",\n        \"教育局\",\n        \"科技局\",\n        \"工信局\",\n        \"公安局\",\n        \"民政局\",\n        \"司法局\",\n        \"财政局\",\n        \"人社局\",\n        \"自然资源局\",\n        \"生态环境局\",\n        \"住建局\",\n        \"交通运输局\",\n        \"水务局\",\n        \"农业农村局\",\n        \"商务局\",\n        \"文旅局\",\n        \"卫健委\",\n        \"应急管理局\",\n        \"审计局\",\n        \"市场监管局\",\n        \"统计局\",\n        \"医保局\"\n    ];\n    // 权威数据领域\n    const authorityData = [\n        {\n            area: \"人口信息\",\n            coverage: \"100%\",\n            source: \"公安、民政、人社等部门\",\n            color: \"blue\"\n        },\n        {\n            area: \"企业信息\",\n            coverage: \"100%\",\n            source: \"市场监管、税务、工信等部门\",\n            color: \"green\"\n        },\n        {\n            area: \"地理信息\",\n            coverage: \"100%\",\n            source: \"自然资源、住建、交通等部门\",\n            color: \"purple\"\n        },\n        {\n            area: \"经济数据\",\n            coverage: \"95%\",\n            source: \"统计、财政、发改等部门\",\n            color: \"orange\"\n        },\n        {\n            area: \"社会事业\",\n            coverage: \"90%\",\n            source: \"教育、卫健、文旅等部门\",\n            color: \"pink\"\n        },\n        {\n            area: \"环境数据\",\n            coverage: \"100%\",\n            source: \"生态环境、水务、应急等部门\",\n            color: \"teal\"\n        }\n    ];\n    // 目标客户\n    const targetUsers = [\n        {\n            type: \"政府决策层\",\n            desc: \"为各级领导提供数据支撑和分析报告，辅助科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            features: [\n                \"实时数据大屏\",\n                \"决策分析报告\",\n                \"趋势预测分析\"\n            ]\n        },\n        {\n            type: \"业务部门\",\n            desc: \"各委办局日常业务数据管理和跨部门协同应用\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            features: [\n                \"业务数据管理\",\n                \"跨部门协同\",\n                \"流程优化\"\n            ]\n        },\n        {\n            type: \"技术人员\",\n            desc: \"数据开发、运维和技术支持人员的专业工具\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            features: [\n                \"数据开发工具\",\n                \"系统监控\",\n                \"技术支持\"\n            ]\n        },\n        {\n            type: \"公众服务\",\n            desc: \"为市民和企业提供便民服务和信息查询\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            features: [\n                \"信息查询\",\n                \"在线服务\",\n                \"便民应用\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center mb-20 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-blue-50/50 backdrop-blur-sm px-6 py-3 rounded-full mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-blue-700\",\n                                                children: \"智慧政务 \\xb7 数据驱动 \\xb7 创新未来\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl md:text-7xl font-bold mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: \"云宇政数平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"统一的政务数据管理与服务平台，为政府数字化转型提供全方位的数据支撑， 实现跨部门数据共享、智能分析决策和高效政务服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-8 mb-12 text-lg text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"24个委办局接入\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"2.4TB数据存储\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"99.9%系统可用性\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"平台核心能力\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为政务数字化转型提供全方位的技术支撑\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                                        children: [\n                                            capabilities.map((capability, index)=>{\n                                                const Icon = capability.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 100, \"ms\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-br \".concat(capability.gradient, \" rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                                children: capability.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed\",\n                                                                children: capability.description\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, capability.title, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/dashboard\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 group border border-blue-300/50 cursor-pointer transform hover:scale-105\",\n                                                            style: {\n                                                                animationDelay: \"400ms\"\n                                                            },\n                                                            onMouseEnter: ()=>setShowModuleOverlay(true),\n                                                            onMouseLeave: ()=>setShowModuleOverlay(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-white mb-4\",\n                                                                        children: \"立即体验\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-100 leading-relaxed mb-4\",\n                                                                        children: \"悬停查看业务模块，点击进入云宇政数平台\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold flex items-center group-hover:translate-x-1 transition-transform\",\n                                                                            children: [\n                                                                                \"进入平台\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"w-5 h-5 ml-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showModuleOverlay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 left-full ml-4 w-[800px] bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50 p-8 z-50 animate-fade-in\",\n                                                        onMouseEnter: ()=>setShowModuleOverlay(true),\n                                                        onMouseLeave: ()=>setShowModuleOverlay(false),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                                        children: \"业务模块快速入口\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"选择您需要的功能模块，直接进入对应的业务操作界面\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 gap-4\",\n                                                                children: businessModules.map((module, index)=>{\n                                                                    const Icon = module.icon;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: module.href,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/80 rounded-2xl p-4 hover:bg-white hover:shadow-lg transition-all duration-300 group cursor-pointer border border-gray-100\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-start space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-shrink-0\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"w-12 h-12 bg-gradient-to-br \".concat(module.gradient, \" rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300\"),\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                                                className: \"w-6 h-6 text-white\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 323,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 322,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1 min-w-0\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-2xl\",\n                                                                                                        children: module.logo\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 328,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                        className: \"text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors\",\n                                                                                                        children: module.name\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 329,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 327,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600 mb-2 line-clamp-2\",\n                                                                                                children: module.description\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 333,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex flex-wrap gap-1\",\n                                                                                                children: module.features.slice(0, 2).map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                                                                        children: feature\n                                                                                                    }, idx, false, {\n                                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 338,\n                                                                                                        columnNumber: 39\n                                                                                                    }, this))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 336,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 326,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                        className: \"w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-all duration-300 transform group-hover:translate-x-1 flex-shrink-0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 344,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, module.name, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-6 pt-6 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/dashboard\",\n                                                                        className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-5 h-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"进入主控台\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 359,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"200ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"解决的应用场景\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖政务管理的各个关键环节，提升政府治理效能\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                        children: scenarios.map((scenario, index)=>{\n                                            const Icon = scenario.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                                    children: scenario.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: scenario.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, scenario.title, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"400ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"数据来源单位\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖全市24个主要委办局，构建统一的数据生态体系\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6\",\n                                            children: dataSourceUnits.map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 rounded-xl bg-gray-50/50 hover:bg-blue-50/50 transition-all duration-300 group\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-3 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors\",\n                                                            children: unit\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, unit, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"600ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"权威数据覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"全面覆盖政务核心数据领域，确保数据权威性和完整性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: authorityData.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: data.area\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-blue-600\",\n                                                                children: data.coverage\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: data.source\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: data.coverage\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, data.area, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"800ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"服务对象\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为不同类型用户提供专业化的数据服务和解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                        children: targetUsers.map((user, index)=>{\n                                            const Icon = user.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group text-center border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                        children: user.type\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                        children: user.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: user.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    feature\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1000ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-4 rounded-2xl mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                        children: \"业务模块入口\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                children: \"选择您需要的功能模块\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl text-gray-600\",\n                                                children: \"点击下方模块卡片，直接进入对应的业务操作界面\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-3xl p-12 border-2 border-blue-200/50 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                                children: [\n                                                    {\n                                                        name: \"数据大屏\",\n                                                        description: \"实时数据可视化展示，支持多种图表和指标监控\",\n                                                        href: \"/dashboard/screen\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                        gradient: \"from-blue-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"可视化图表\",\n                                                            \"大屏展示\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"报表分析\",\n                                                        description: \"多维度数据分析报表，支持自定义报表生成\",\n                                                        href: \"/dashboard/reports\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                        gradient: \"from-green-500 to-emerald-500\",\n                                                        features: [\n                                                            \"多维分析\",\n                                                            \"自定义报表\",\n                                                            \"数据导出\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据采集\",\n                                                        description: \"多源数据采集接入，支持实时和批量数据导入\",\n                                                        href: \"/dashboard/collection\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                                        gradient: \"from-purple-500 to-violet-500\",\n                                                        features: [\n                                                            \"多源接入\",\n                                                            \"实时采集\",\n                                                            \"数据清洗\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据汇聚\",\n                                                        description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n                                                        href: \"/dashboard/aggregation\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                        gradient: \"from-orange-500 to-amber-500\",\n                                                        features: [\n                                                            \"数据整合\",\n                                                            \"标准化\",\n                                                            \"质量控制\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据治理\",\n                                                        description: \"数据质量管控，数据清洗、去重、标准化处理\",\n                                                        href: \"/dashboard/governance\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                        gradient: \"from-red-500 to-rose-500\",\n                                                        features: [\n                                                            \"质量管控\",\n                                                            \"数据清洗\",\n                                                            \"合规管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"资源池管理\",\n                                                        description: \"数据资源统一管理，资源目录和权限控制\",\n                                                        href: \"/dashboard/resources\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                                        gradient: \"from-indigo-500 to-blue-500\",\n                                                        features: [\n                                                            \"资源目录\",\n                                                            \"权限管理\",\n                                                            \"存储优化\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"设备监控\",\n                                                        description: \"服务器设备实时监控，性能指标和告警管理\",\n                                                        href: \"/dashboard/monitoring\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                        gradient: \"from-teal-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"性能分析\",\n                                                            \"告警管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"网络管理\",\n                                                        description: \"多物理网络管理，网络拓扑和流量监控\",\n                                                        href: \"/dashboard/network\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                        gradient: \"from-pink-500 to-rose-500\",\n                                                        features: [\n                                                            \"网络拓扑\",\n                                                            \"流量监控\",\n                                                            \"安全管理\"\n                                                        ]\n                                                    }\n                                                ].map((module, index)=>{\n                                                    const Icon = module.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: module.href,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:scale-105 border border-white/50\",\n                                                            style: {\n                                                                animationDelay: \"\".concat(index * 100, \"ms\")\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative overflow-hidden text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-br \".concat(module.gradient, \" opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative z-10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-20 h-20 bg-gradient-to-br \".concat(module.gradient, \" rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                                    className: \"w-10 h-10 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors\",\n                                                                                children: module.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 text-sm mb-6 leading-relaxed\",\n                                                                                children: module.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2 mb-6\",\n                                                                                children: module.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-center text-xs text-gray-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                className: \"w-3 h-3 text-green-500 mr-2\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 595,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            feature\n                                                                                        ]\n                                                                                    }, idx, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 594,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-lg font-bold text-blue-600 group-hover:text-blue-700 transition-colors flex items-center\",\n                                                                                    children: [\n                                                                                        \"立即使用\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 601,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, module.name, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mt-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center space-x-3 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: \"选择任意模块开始您的数据管理之旅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1200ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-6\",\n                                            children: \"准备好开始了吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl mb-12 text-blue-100\",\n                                            children: \"立即体验云宇政数平台，开启您的智慧政务数据管理之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    className: \"bg-white text-blue-600 hover:bg-blue-50 font-bold py-5 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"进入平台\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500/20 backdrop-blur-sm text-white hover:bg-blue-500/30 font-medium py-5 px-12 rounded-2xl transition-all duration-300 border border-white/20 flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"了解更多\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-t border-white/20 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg mb-3\",\n                                        children: \"\\xa9 2024 云宇政数平台. 保留所有权利.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base\",\n                                        children: \"为政府数字化转型提供专业的数据管理服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"RGHj73ERCyzXB1ChjWpiTxNNqNw=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QjtBQUNJO0FBd0JYO0FBRU4sU0FBU3VCOztJQUN0QixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUUzRCxTQUFTO0lBQ1QsTUFBTXlCLGtCQUFrQjtRQUN0QjtZQUNFQyxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxNQUFNcEIsa1BBQU9BO1lBQ2JxQixVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsVUFBVTtnQkFBQztnQkFBUTtnQkFBUzthQUFPO1FBQ3JDO1FBQ0E7WUFDRU4sTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsTUFBTW5CLGtQQUFRQTtZQUNkb0IsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQVE7Z0JBQVM7YUFBTztRQUNyQztRQUNBO1lBQ0VOLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLE1BQU0zQixrUEFBUUE7WUFDZDRCLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxVQUFVO2dCQUFDO2dCQUFRO2dCQUFRO2FBQU87UUFDcEM7UUFDQTtZQUNFTixNQUFNO1lBQ05DLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxNQUFNbEIsa1BBQVFBO1lBQ2RtQixVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsVUFBVTtnQkFBQztnQkFBUTtnQkFBTzthQUFPO1FBQ25DO1FBQ0E7WUFDRU4sTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsTUFBTTVCLGtQQUFNQTtZQUNaNkIsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQVE7Z0JBQVE7YUFBTztRQUNwQztRQUNBO1lBQ0VOLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLE1BQU1mLGtQQUFTQTtZQUNmZ0IsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQVE7Z0JBQVE7YUFBTztRQUNwQztRQUNBO1lBQ0VOLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLE1BQU1qQixrUEFBTUE7WUFDWmtCLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxVQUFVO2dCQUFDO2dCQUFRO2dCQUFRO2FBQU87UUFDcEM7UUFDQTtZQUNFTixNQUFNO1lBQ05DLGFBQWE7WUFDYkMsTUFBTTtZQUNOQyxNQUFNaEIsbVBBQU9BO1lBQ2JpQixVQUFVO1lBQ1ZDLE1BQU07WUFDTkMsVUFBVTtnQkFBQztnQkFBUTtnQkFBUTthQUFPO1FBQ3BDO0tBQ0Q7SUFFRCxPQUFPO0lBQ1AsTUFBTUMsZUFBZTtRQUNuQjtZQUNFSixNQUFNM0Isa1BBQVFBO1lBQ2RnQyxPQUFPO1lBQ1BQLGFBQWE7WUFDYkcsVUFBVTtRQUNaO1FBQ0E7WUFDRUQsTUFBTTFCLG1QQUFTQTtZQUNmK0IsT0FBTztZQUNQUCxhQUFhO1lBQ2JHLFVBQVU7UUFDWjtRQUNBO1lBQ0VELE1BQU01QixrUEFBTUE7WUFDWmlDLE9BQU87WUFDUFAsYUFBYTtZQUNiRyxVQUFVO1FBQ1o7UUFDQTtZQUNFRCxNQUFNdkIsbVBBQUtBO1lBQ1g0QixPQUFPO1lBQ1BQLGFBQWE7WUFDYkcsVUFBVTtRQUNaO0tBQ0Q7SUFFRCxRQUFRO0lBQ1IsTUFBTUssWUFBWTtRQUNoQjtZQUNFRCxPQUFPO1lBQ1BQLGFBQWE7WUFDYkUsTUFBTVYsbVBBQVVBO1FBQ2xCO1FBQ0E7WUFDRWUsT0FBTztZQUNQUCxhQUFhO1lBQ2JFLE1BQU12QixtUEFBS0E7UUFDYjtRQUNBO1lBQ0U0QixPQUFPO1lBQ1BQLGFBQWE7WUFDYkUsTUFBTXpCLG1QQUFLQTtRQUNiO1FBQ0E7WUFDRThCLE9BQU87WUFDUFAsYUFBYTtZQUNiRSxNQUFNVCxtUEFBR0E7UUFDWDtLQUNEO0lBRUQsU0FBUztJQUNULE1BQU1nQixrQkFBa0I7UUFDdEI7UUFBVTtRQUFTO1FBQU87UUFBTztRQUFPO1FBQ3hDO1FBQU87UUFBTztRQUFPO1FBQU87UUFBUztRQUNyQztRQUFPO1FBQVM7UUFBTztRQUFTO1FBQU87UUFDdkM7UUFBTztRQUFTO1FBQU87UUFBUztRQUFPO0tBQ3hDO0lBRUQsU0FBUztJQUNULE1BQU1DLGdCQUFnQjtRQUNwQjtZQUFFQyxNQUFNO1lBQVFDLFVBQVU7WUFBUUMsUUFBUTtZQUFlQyxPQUFPO1FBQU87UUFDdkU7WUFBRUgsTUFBTTtZQUFRQyxVQUFVO1lBQVFDLFFBQVE7WUFBaUJDLE9BQU87UUFBUTtRQUMxRTtZQUFFSCxNQUFNO1lBQVFDLFVBQVU7WUFBUUMsUUFBUTtZQUFpQkMsT0FBTztRQUFTO1FBQzNFO1lBQUVILE1BQU07WUFBUUMsVUFBVTtZQUFPQyxRQUFRO1lBQWVDLE9BQU87UUFBUztRQUN4RTtZQUFFSCxNQUFNO1lBQVFDLFVBQVU7WUFBT0MsUUFBUTtZQUFlQyxPQUFPO1FBQU87UUFDdEU7WUFBRUgsTUFBTTtZQUFRQyxVQUFVO1lBQVFDLFFBQVE7WUFBaUJDLE9BQU87UUFBTztLQUMxRTtJQUVELE9BQU87SUFDUCxNQUFNQyxjQUFjO1FBQ2xCO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOZixNQUFNWixtUEFBS0E7WUFDWGUsVUFBVTtnQkFBQztnQkFBVTtnQkFBVTthQUFTO1FBQzFDO1FBQ0E7WUFDRVcsTUFBTTtZQUNOQyxNQUFNO1lBQ05mLE1BQU14QixtUEFBU0E7WUFDZjJCLFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVM7YUFBTztRQUN2QztRQUNBO1lBQ0VXLE1BQU07WUFDTkMsTUFBTTtZQUNOZixNQUFNUixtUEFBR0E7WUFDVFcsVUFBVTtnQkFBQztnQkFBVTtnQkFBUTthQUFPO1FBQ3RDO1FBQ0E7WUFDRVcsTUFBTTtZQUNOQyxNQUFNO1lBQ05mLE1BQU16QixtUEFBS0E7WUFDWDRCLFVBQVU7Z0JBQUM7Z0JBQVE7Z0JBQVE7YUFBTztRQUNwQztLQUNEO0lBRUQscUJBQ0UsOERBQUNhO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVO3dCQUFrSUMsT0FBTzs0QkFBRUMsZ0JBQWdCO3dCQUFLOzs7Ozs7Ozs7Ozs7MEJBR2pMLDhEQUFDSDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNHO3dCQUFLSCxXQUFVOzswQ0FFZCw4REFBQ0k7Z0NBQVFKLFdBQVU7O2tEQUNqQiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDL0IsbVBBQVFBO2dEQUFDK0IsV0FBVTs7Ozs7OzBEQUNwQiw4REFBQ0s7Z0RBQUtMLFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7a0RBR3RELDhEQUFDTTt3Q0FBR04sV0FBVTtrREFDWiw0RUFBQ0s7NENBQUtMLFdBQVU7c0RBQTZFOzs7Ozs7Ozs7OztrREFHL0YsOERBQUNPO3dDQUFFUCxXQUFVO2tEQUFpRTs7Ozs7O2tEQUs5RSw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN0QyxtUEFBV0E7d0RBQUNzQyxXQUFVOzs7Ozs7a0VBQ3ZCLDhEQUFDSztrRUFBSzs7Ozs7Ozs7Ozs7OzBEQUVSLDhEQUFDTjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN0QyxtUEFBV0E7d0RBQUNzQyxXQUFVOzs7Ozs7a0VBQ3ZCLDhEQUFDSztrRUFBSzs7Ozs7Ozs7Ozs7OzBEQUVSLDhEQUFDTjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN0QyxtUEFBV0E7d0RBQUNzQyxXQUFVOzs7Ozs7a0VBQ3ZCLDhEQUFDSztrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1aLDhEQUFDRDtnQ0FBUUosV0FBVTs7a0RBQ2pCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFHUixXQUFVOzBEQUF3Qzs7Ozs7OzBEQUN0RCw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7NENBQ1piLGFBQWFzQixHQUFHLENBQUMsQ0FBQ0MsWUFBWUM7Z0RBQzdCLE1BQU1DLE9BQU9GLFdBQVczQixJQUFJO2dEQUM1QixxQkFDRSw4REFBQ2dCO29EQUVDQyxXQUFVO29EQUNWQyxPQUFPO3dEQUFFQyxnQkFBZ0IsR0FBZSxPQUFaUyxRQUFRLEtBQUk7b0RBQUk7OERBRTVDLDRFQUFDWjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFXLCtCQUFtRCxPQUFwQlUsV0FBVzFCLFFBQVEsRUFBQzswRUFDakUsNEVBQUM0QjtvRUFBS1osV0FBVTs7Ozs7Ozs7Ozs7MEVBRWxCLDhEQUFDYTtnRUFBR2IsV0FBVTswRUFBd0NVLFdBQVd0QixLQUFLOzs7Ozs7MEVBQ3RFLDhEQUFDbUI7Z0VBQUVQLFdBQVU7MEVBQWlDVSxXQUFXN0IsV0FBVzs7Ozs7Ozs7Ozs7O21EQVRqRTZCLFdBQVd0QixLQUFLOzs7Ozs0Q0FhM0I7MERBR0EsOERBQUNXO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQy9DLGlEQUFJQTt3REFBQzZCLE1BQUs7a0VBQ1QsNEVBQUNpQjs0REFDQ0MsV0FBVTs0REFDVkMsT0FBTztnRUFBRUMsZ0JBQWdCOzREQUFROzREQUNqQ1ksY0FBYyxJQUFNcEMscUJBQXFCOzREQUN6Q3FDLGNBQWMsSUFBTXJDLHFCQUFxQjtzRUFFekMsNEVBQUNxQjtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDNUIsbVBBQUdBOzRFQUFDNEIsV0FBVTs7Ozs7Ozs7Ozs7a0ZBRWpCLDhEQUFDYTt3RUFBR2IsV0FBVTtrRkFBb0M7Ozs7OztrRkFDbEQsOERBQUNPO3dFQUFFUCxXQUFVO2tGQUFxQzs7Ozs7O2tGQUNsRCw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNLOzRFQUFLTCxXQUFVOztnRkFBd0Y7OEZBRXRHLDhEQUFDdkMsbVBBQVVBO29GQUFDdUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQVEvQnZCLG1DQUNDLDhEQUFDc0I7d0RBQ0NDLFdBQVU7d0RBQ1ZjLGNBQWMsSUFBTXBDLHFCQUFxQjt3REFDekNxQyxjQUFjLElBQU1yQyxxQkFBcUI7OzBFQUV6Qyw4REFBQ3FCO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ2E7d0VBQUdiLFdBQVU7a0ZBQXdDOzs7Ozs7a0ZBQ3RELDhEQUFDTzt3RUFBRVAsV0FBVTtrRkFBZ0I7Ozs7Ozs7Ozs7OzswRUFHL0IsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNackIsZ0JBQWdCOEIsR0FBRyxDQUFDLENBQUNPLFFBQVFMO29FQUM1QixNQUFNQyxPQUFPSSxPQUFPakMsSUFBSTtvRUFDeEIscUJBQ0UsOERBQUM5QixpREFBSUE7d0VBQW1CNkIsTUFBTWtDLE9BQU9sQyxJQUFJO2tGQUN2Qyw0RUFBQ2lCOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDRDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNEO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVywrQkFBK0MsT0FBaEJnQixPQUFPaEMsUUFBUSxFQUFDO3NHQUM3RCw0RUFBQzRCO2dHQUFLWixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O2tHQUdwQiw4REFBQ0Q7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDRDtnR0FBSUMsV0FBVTs7a0hBQ2IsOERBQUNLO3dHQUFLTCxXQUFVO2tIQUFZZ0IsT0FBTy9CLElBQUk7Ozs7OztrSEFDdkMsOERBQUNnQzt3R0FBR2pCLFdBQVU7a0hBQ1hnQixPQUFPcEMsSUFBSTs7Ozs7Ozs7Ozs7OzBHQUdoQiw4REFBQzJCO2dHQUFFUCxXQUFVOzBHQUNWZ0IsT0FBT25DLFdBQVc7Ozs7OzswR0FFckIsOERBQUNrQjtnR0FBSUMsV0FBVTswR0FDWmdCLE9BQU85QixRQUFRLENBQUNnQyxLQUFLLENBQUMsR0FBRyxHQUFHVCxHQUFHLENBQUMsQ0FBQ1UsU0FBU0Msb0JBQ3pDLDhEQUFDZjt3R0FBZUwsV0FBVTtrSEFDdkJtQjt1R0FEUUM7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBTWpCLDhEQUFDM0QsbVBBQVVBO3dGQUFDdUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7dUVBMUJqQmdCLE9BQU9wQyxJQUFJOzs7OztnRUErQjFCOzs7Ozs7MEVBR0YsOERBQUNtQjtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUMvQyxpREFBSUE7d0VBQ0g2QixNQUFLO3dFQUNMa0IsV0FBVTs7MEZBRVYsOERBQUM1QixtUEFBR0E7Z0ZBQUM0QixXQUFVOzs7Ozs7MEZBQ2YsOERBQUNLO2dGQUFLTCxXQUFVOzBGQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FXaEQsOERBQUNJO2dDQUFRSixXQUFVO2dDQUF5QkMsT0FBTztvQ0FBRUMsZ0JBQWdCO2dDQUFROztrREFDM0UsOERBQUNIO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQXdDOzs7Ozs7MERBQ3RELDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaWCxVQUFVb0IsR0FBRyxDQUFDLENBQUNZLFVBQVVWOzRDQUN4QixNQUFNQyxPQUFPUyxTQUFTdEMsSUFBSTs0Q0FDMUIscUJBQ0UsOERBQUNnQjtnREFFQ0MsV0FBVTtnREFDVkMsT0FBTztvREFBRUMsZ0JBQWdCLEdBQWUsT0FBWlMsUUFBUSxLQUFJO2dEQUFJOzBEQUU1Qyw0RUFBQ1o7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ1k7Z0VBQUtaLFdBQVU7Ozs7Ozs7Ozs7O3NFQUVsQiw4REFBQ0Q7OzhFQUNDLDhEQUFDYztvRUFBR2IsV0FBVTs4RUFBd0NxQixTQUFTakMsS0FBSzs7Ozs7OzhFQUNwRSw4REFBQ21CO29FQUFFUCxXQUFVOzhFQUFpQ3FCLFNBQVN4QyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBVmpFd0MsU0FBU2pDLEtBQUs7Ozs7O3dDQWV6Qjs7Ozs7Ozs7Ozs7OzBDQUtKLDhEQUFDZ0I7Z0NBQVFKLFdBQVU7Z0NBQXlCQyxPQUFPO29DQUFFQyxnQkFBZ0I7Z0NBQVE7O2tEQUMzRSw4REFBQ0g7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDUTtnREFBR1IsV0FBVTswREFBd0M7Ozs7OzswREFDdEQsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVO3NEQUNaVixnQkFBZ0JtQixHQUFHLENBQUMsQ0FBQ2EsTUFBTVgsc0JBQzFCLDhEQUFDWjtvREFFQ0MsV0FBVTtvREFDVkMsT0FBTzt3REFBRUMsZ0JBQWdCLEdBQWMsT0FBWFMsUUFBUSxJQUFHO29EQUFJOztzRUFFM0MsOERBQUNwRCxtUEFBU0E7NERBQUN5QyxXQUFVOzs7Ozs7c0VBQ3JCLDhEQUFDSzs0REFBS0wsV0FBVTtzRUFBaUZzQjs7Ozs7OzttREFMNUZBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBYWYsOERBQUNsQjtnQ0FBUUosV0FBVTtnQ0FBeUJDLE9BQU87b0NBQUVDLGdCQUFnQjtnQ0FBUTs7a0RBQzNFLDhEQUFDSDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFHUixXQUFVOzBEQUF3Qzs7Ozs7OzBEQUN0RCw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWlQsY0FBY2tCLEdBQUcsQ0FBQyxDQUFDYyxNQUFNWixzQkFDeEIsOERBQUNaO2dEQUVDQyxXQUFVO2dEQUNWQyxPQUFPO29EQUFFQyxnQkFBZ0IsR0FBZSxPQUFaUyxRQUFRLEtBQUk7Z0RBQUk7O2tFQUU1Qyw4REFBQ1o7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYTtnRUFBR2IsV0FBVTswRUFBbUN1QixLQUFLL0IsSUFBSTs7Ozs7OzBFQUMxRCw4REFBQ2E7Z0VBQUtMLFdBQVU7MEVBQW9DdUIsS0FBSzlCLFFBQVE7Ozs7Ozs7Ozs7OztrRUFFbkUsOERBQUNjO3dEQUFFUCxXQUFVO2tFQUFzQnVCLEtBQUs3QixNQUFNOzs7Ozs7a0VBQzlDLDhEQUFDSzt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0Q7NERBQ0NDLFdBQVU7NERBQ1ZDLE9BQU87Z0VBQUV1QixPQUFPRCxLQUFLOUIsUUFBUTs0REFBQzs7Ozs7Ozs7Ozs7OytDQVo3QjhCLEtBQUsvQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzBDQXFCdEIsOERBQUNZO2dDQUFRSixXQUFVO2dDQUF5QkMsT0FBTztvQ0FBRUMsZ0JBQWdCO2dDQUFROztrREFDM0UsOERBQUNIO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQXdDOzs7Ozs7MERBQ3RELDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaSixZQUFZYSxHQUFHLENBQUMsQ0FBQ2dCLE1BQU1kOzRDQUN0QixNQUFNQyxPQUFPYSxLQUFLMUMsSUFBSTs0Q0FDdEIscUJBQ0UsOERBQUNnQjtnREFFQ0MsV0FBVTtnREFDVkMsT0FBTztvREFBRUMsZ0JBQWdCLEdBQWUsT0FBWlMsUUFBUSxLQUFJO2dEQUFJOztrRUFFNUMsOERBQUNaO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDWTs0REFBS1osV0FBVTs7Ozs7Ozs7Ozs7a0VBRWxCLDhEQUFDYTt3REFBR2IsV0FBVTtrRUFBd0N5QixLQUFLNUIsSUFBSTs7Ozs7O2tFQUMvRCw4REFBQ1U7d0RBQUVQLFdBQVU7a0VBQXNDeUIsS0FBSzNCLElBQUk7Ozs7OztrRUFDNUQsOERBQUNDO3dEQUFJQyxXQUFVO2tFQUNaeUIsS0FBS3ZDLFFBQVEsQ0FBQ3VCLEdBQUcsQ0FBQyxDQUFDVSxTQUFTQyxvQkFDM0IsOERBQUNyQjtnRUFBY0MsV0FBVTs7a0ZBQ3ZCLDhEQUFDdEMsbVBBQVdBO3dFQUFDc0MsV0FBVTs7Ozs7O29FQUN0Qm1COzsrREFGT0M7Ozs7Ozs7Ozs7OytDQVhUSyxLQUFLNUIsSUFBSTs7Ozs7d0NBbUJwQjs7Ozs7Ozs7Ozs7OzBDQUtKLDhEQUFDTztnQ0FBUUosV0FBVTtnQ0FBeUJDLE9BQU87b0NBQUVDLGdCQUFnQjtnQ0FBUzs7a0RBQzVFLDhEQUFDSDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzlCLG1QQUFNQTt3REFBQzhCLFdBQVU7Ozs7OztrRUFDbEIsOERBQUNLO3dEQUFLTCxXQUFVO2tFQUFnRzs7Ozs7Ozs7Ozs7OzBEQUVsSCw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQXdDOzs7Ozs7MERBQ3RELDhEQUFDTztnREFBRVAsV0FBVTswREFBeUI7Ozs7Ozs7Ozs7OztrREFJeEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ1o7b0RBQ0M7d0RBQ0VwQixNQUFNO3dEQUNOQyxhQUFhO3dEQUNiQyxNQUFNO3dEQUNOQyxNQUFNcEIsa1BBQU9BO3dEQUNicUIsVUFBVTt3REFDVkUsVUFBVTs0REFBQzs0REFBUTs0REFBUzt5REFBTztvREFDckM7b0RBQ0E7d0RBQ0VOLE1BQU07d0RBQ05DLGFBQWE7d0RBQ2JDLE1BQU07d0RBQ05DLE1BQU1uQixrUEFBUUE7d0RBQ2RvQixVQUFVO3dEQUNWRSxVQUFVOzREQUFDOzREQUFROzREQUFTO3lEQUFPO29EQUNyQztvREFDQTt3REFDRU4sTUFBTTt3REFDTkMsYUFBYTt3REFDYkMsTUFBTTt3REFDTkMsTUFBTTNCLGtQQUFRQTt3REFDZDRCLFVBQVU7d0RBQ1ZFLFVBQVU7NERBQUM7NERBQVE7NERBQVE7eURBQU87b0RBQ3BDO29EQUNBO3dEQUNFTixNQUFNO3dEQUNOQyxhQUFhO3dEQUNiQyxNQUFNO3dEQUNOQyxNQUFNbEIsa1BBQVFBO3dEQUNkbUIsVUFBVTt3REFDVkUsVUFBVTs0REFBQzs0REFBUTs0REFBTzt5REFBTztvREFDbkM7b0RBQ0E7d0RBQ0VOLE1BQU07d0RBQ05DLGFBQWE7d0RBQ2JDLE1BQU07d0RBQ05DLE1BQU01QixrUEFBTUE7d0RBQ1o2QixVQUFVO3dEQUNWRSxVQUFVOzREQUFDOzREQUFROzREQUFRO3lEQUFPO29EQUNwQztvREFDQTt3REFDRU4sTUFBTTt3REFDTkMsYUFBYTt3REFDYkMsTUFBTTt3REFDTkMsTUFBTWYsa1BBQVNBO3dEQUNmZ0IsVUFBVTt3REFDVkUsVUFBVTs0REFBQzs0REFBUTs0REFBUTt5REFBTztvREFDcEM7b0RBQ0E7d0RBQ0VOLE1BQU07d0RBQ05DLGFBQWE7d0RBQ2JDLE1BQU07d0RBQ05DLE1BQU1qQixrUEFBTUE7d0RBQ1prQixVQUFVO3dEQUNWRSxVQUFVOzREQUFDOzREQUFROzREQUFRO3lEQUFPO29EQUNwQztvREFDQTt3REFDRU4sTUFBTTt3REFDTkMsYUFBYTt3REFDYkMsTUFBTTt3REFDTkMsTUFBTWhCLG1QQUFPQTt3REFDYmlCLFVBQVU7d0RBQ1ZFLFVBQVU7NERBQUM7NERBQVE7NERBQVE7eURBQU87b0RBQ3BDO2lEQUNELENBQUN1QixHQUFHLENBQUMsQ0FBQ08sUUFBUUw7b0RBQ2IsTUFBTUMsT0FBT0ksT0FBT2pDLElBQUk7b0RBQ3hCLHFCQUNFLDhEQUFDOUIsaURBQUlBO3dEQUFtQjZCLE1BQU1rQyxPQUFPbEMsSUFBSTtrRUFDdkMsNEVBQUNpQjs0REFDQ0MsV0FBVTs0REFDVkMsT0FBTztnRUFBRUMsZ0JBQWdCLEdBQWUsT0FBWlMsUUFBUSxLQUFJOzREQUFJO3NFQUU1Qyw0RUFBQ1o7Z0VBQUlDLFdBQVU7O2tGQUViLDhEQUFDRDt3RUFBSUMsV0FBVyxzQ0FBc0QsT0FBaEJnQixPQUFPaEMsUUFBUSxFQUFDOzs7Ozs7a0ZBRXRFLDhEQUFDZTt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEO2dGQUFJQyxXQUFXLCtCQUErQyxPQUFoQmdCLE9BQU9oQyxRQUFRLEVBQUM7MEZBQzdELDRFQUFDNEI7b0ZBQUtaLFdBQVU7Ozs7Ozs7Ozs7OzBGQUdsQiw4REFBQ2E7Z0ZBQUdiLFdBQVU7MEZBQ1hnQixPQUFPcEMsSUFBSTs7Ozs7OzBGQUdkLDhEQUFDMkI7Z0ZBQUVQLFdBQVU7MEZBQ1ZnQixPQUFPbkMsV0FBVzs7Ozs7OzBGQUdyQiw4REFBQ2tCO2dGQUFJQyxXQUFVOzBGQUNaZ0IsT0FBTzlCLFFBQVEsQ0FBQ3VCLEdBQUcsQ0FBQyxDQUFDVSxTQUFTQyxvQkFDN0IsOERBQUNyQjt3RkFBY0MsV0FBVTs7MEdBQ3ZCLDhEQUFDdEMsbVBBQVdBO2dHQUFDc0MsV0FBVTs7Ozs7OzRGQUN0Qm1COzt1RkFGT0M7Ozs7Ozs7Ozs7MEZBT2QsOERBQUNyQjtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ0s7b0ZBQUtMLFdBQVU7O3dGQUFnRztzR0FFOUcsOERBQUN2QyxtUEFBVUE7NEZBQUN1QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFNNUIsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDRDs0RUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REF6Q1pnQixPQUFPcEMsSUFBSTs7Ozs7Z0RBK0MxQjs7Ozs7OzBEQUlGLDhEQUFDbUI7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzdCLG1QQUFLQTs0REFBQzZCLFdBQVU7Ozs7OztzRUFDakIsOERBQUNLOzREQUFLTCxXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPbEMsOERBQUNJO2dDQUFRSixXQUFVO2dDQUErQkMsT0FBTztvQ0FBRUMsZ0JBQWdCO2dDQUFTOzBDQUNsRiw0RUFBQ0g7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDUTs0Q0FBR1IsV0FBVTtzREFBMEI7Ozs7OztzREFDeEMsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUErQjs7Ozs7O3NEQUc1Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDL0MsaURBQUlBO29EQUNINkIsTUFBSztvREFDTGtCLFdBQVU7O3NFQUVWLDhEQUFDNUIsbVBBQUdBOzREQUFDNEIsV0FBVTs7Ozs7O3dEQUFpQjs7Ozs7Ozs4REFHbEMsOERBQUMwQjtvREFBTzFCLFdBQVU7O3NFQUNoQiw4REFBQ3BDLGtQQUFRQTs0REFBQ29DLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTL0MsOERBQUMyQjt3QkFBTzNCLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTzt3Q0FBRVAsV0FBVTtrREFBZTs7Ozs7O2tEQUM1Qiw4REFBQ087d0NBQUVQLFdBQVU7a0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdkM7R0E1bkJ3QnhCO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFNoaWVsZCxcbiAgRGF0YWJhc2UsXG4gIEJhckNoYXJ0MyxcbiAgVXNlcnMsXG4gIEJ1aWxkaW5nMixcbiAgR2xvYmUsXG4gIEFycm93UmlnaHQsXG4gIENoZWNrQ2lyY2xlLFxuICBNb25pdG9yLFxuICBGaWxlVGV4dCxcbiAgR2l0TWVyZ2UsXG4gIFNlcnZlcixcbiAgTmV0d29yayxcbiAgSGFyZERyaXZlLFxuICBTcGFya2xlcyxcbiAgVGFyZ2V0LFxuICBBd2FyZCxcbiAgWmFwLFxuICBUcmVuZGluZ1VwLFxuICBFeWUsXG4gIExvY2ssXG4gIENwdVxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWVQYWdlKCkge1xuICBjb25zdCBbc2hvd01vZHVsZU92ZXJsYXksIHNldFNob3dNb2R1bGVPdmVybGF5XSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIOS4muWKoeaooeWdl+aVsOaNrlxuICBjb25zdCBidXNpbmVzc01vZHVsZXMgPSBbXG4gICAge1xuICAgICAgbmFtZTogJ+aVsOaNruWkp+WxjycsXG4gICAgICBkZXNjcmlwdGlvbjogJ+WunuaXtuaVsOaNruWPr+inhuWMluWxleekuu+8jOaUr+aMgeWkmuenjeWbvuihqOWSjOaMh+agh+ebkeaOpycsXG4gICAgICBocmVmOiAnL3NjcmVlbicsXG4gICAgICBpY29uOiBNb25pdG9yLFxuICAgICAgZ3JhZGllbnQ6ICdmcm9tLWJsdWUtNTAwIHRvLWN5YW4tNTAwJyxcbiAgICAgIGxvZ286ICfwn5OKJyxcbiAgICAgIGZlYXR1cmVzOiBbJ+WunuaXtuebkeaOpycsICflj6/op4bljJblm77ooagnLCAn5aSn5bGP5bGV56S6J11cbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfmiqXooajliIbmnpAnLFxuICAgICAgZGVzY3JpcHRpb246ICflpJrnu7TluqbmlbDmja7liIbmnpDmiqXooajvvIzmlK/mjIHoh6rlrprkuYnmiqXooajnlJ/miJAnLFxuICAgICAgaHJlZjogJy9yZXBvcnRzJyxcbiAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgZ3JhZGllbnQ6ICdmcm9tLWdyZWVuLTUwMCB0by1lbWVyYWxkLTUwMCcsXG4gICAgICBsb2dvOiAn8J+TiCcsXG4gICAgICBmZWF0dXJlczogWyflpJrnu7TliIbmnpAnLCAn6Ieq5a6a5LmJ5oql6KGoJywgJ+aVsOaNruWvvOWHuiddXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn5pWw5o2u6YeH6ZuGJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn5aSa5rqQ5pWw5o2u6YeH6ZuG5o6l5YWl77yM5pSv5oyB5a6e5pe25ZKM5om56YeP5pWw5o2u5a+85YWlJyxcbiAgICAgIGhyZWY6ICcvY29sbGVjdGlvbicsXG4gICAgICBpY29uOiBEYXRhYmFzZSxcbiAgICAgIGdyYWRpZW50OiAnZnJvbS1wdXJwbGUtNTAwIHRvLXZpb2xldC01MDAnLFxuICAgICAgbG9nbzogJ/CflIQnLFxuICAgICAgZmVhdHVyZXM6IFsn5aSa5rqQ5o6l5YWlJywgJ+WunuaXtumHh+mbhicsICfmlbDmja7muIXmtJcnXVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+aVsOaNruaxh+iBmicsXG4gICAgICBkZXNjcmlwdGlvbjogJ+i3qOezu+e7n+aVsOaNruaVtOWQiOaxh+iBmu+8jOe7n+S4gOaVsOaNruagh+WHhuWSjOagvOW8jycsXG4gICAgICBocmVmOiAnL2FnZ3JlZ2F0aW9uJyxcbiAgICAgIGljb246IEdpdE1lcmdlLFxuICAgICAgZ3JhZGllbnQ6ICdmcm9tLW9yYW5nZS01MDAgdG8tYW1iZXItNTAwJyxcbiAgICAgIGxvZ286ICfwn5SXJyxcbiAgICAgIGZlYXR1cmVzOiBbJ+aVsOaNruaVtOWQiCcsICfmoIflh4bljJYnLCAn6LSo6YeP5o6n5Yi2J11cbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfmlbDmja7msrvnkIYnLFxuICAgICAgZGVzY3JpcHRpb246ICfmlbDmja7otKjph4/nrqHmjqfvvIzmlbDmja7muIXmtJfjgIHljrvph43jgIHmoIflh4bljJblpITnkIYnLFxuICAgICAgaHJlZjogJy9nb3Zlcm5hbmNlJyxcbiAgICAgIGljb246IFNoaWVsZCxcbiAgICAgIGdyYWRpZW50OiAnZnJvbS1yZWQtNTAwIHRvLXJvc2UtNTAwJyxcbiAgICAgIGxvZ286ICfwn5uh77iPJyxcbiAgICAgIGZlYXR1cmVzOiBbJ+i0qOmHj+euoeaOpycsICfmlbDmja7muIXmtJcnLCAn5ZCI6KeE566h55CGJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfotYTmupDmsaDnrqHnkIYnLFxuICAgICAgZGVzY3JpcHRpb246ICfmlbDmja7otYTmupDnu5/kuIDnrqHnkIbvvIzotYTmupDnm67lvZXlkozmnYPpmZDmjqfliLYnLFxuICAgICAgaHJlZjogJy9yZXNvdXJjZXMnLFxuICAgICAgaWNvbjogSGFyZERyaXZlLFxuICAgICAgZ3JhZGllbnQ6ICdmcm9tLWluZGlnby01MDAgdG8tYmx1ZS01MDAnLFxuICAgICAgbG9nbzogJ/Cfkr4nLFxuICAgICAgZmVhdHVyZXM6IFsn6LWE5rqQ55uu5b2VJywgJ+adg+mZkOeuoeeQhicsICflrZjlgqjkvJjljJYnXVxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+iuvuWkh+ebkeaOpycsXG4gICAgICBkZXNjcmlwdGlvbjogJ+acjeWKoeWZqOiuvuWkh+WunuaXtuebkeaOp++8jOaAp+iDveaMh+agh+WSjOWRiuitpueuoeeQhicsXG4gICAgICBocmVmOiAnL21vbml0b3JpbmcnLFxuICAgICAgaWNvbjogU2VydmVyLFxuICAgICAgZ3JhZGllbnQ6ICdmcm9tLXRlYWwtNTAwIHRvLWN5YW4tNTAwJyxcbiAgICAgIGxvZ286ICfwn5al77iPJyxcbiAgICAgIGZlYXR1cmVzOiBbJ+WunuaXtuebkeaOpycsICfmgKfog73liIbmnpAnLCAn5ZGK6K2m566h55CGJ11cbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfnvZHnu5znrqHnkIYnLFxuICAgICAgZGVzY3JpcHRpb246ICflpJrniannkIbnvZHnu5znrqHnkIbvvIznvZHnu5zmi5PmiZHlkozmtYHph4/nm5HmjqcnLFxuICAgICAgaHJlZjogJy9uZXR3b3JrJyxcbiAgICAgIGljb246IE5ldHdvcmssXG4gICAgICBncmFkaWVudDogJ2Zyb20tcGluay01MDAgdG8tcm9zZS01MDAnLFxuICAgICAgbG9nbzogJ/CfjJAnLFxuICAgICAgZmVhdHVyZXM6IFsn572R57uc5ouT5omRJywgJ+a1gemHj+ebkeaOpycsICflronlhajnrqHnkIYnXVxuICAgIH1cbiAgXVxuXG4gIC8vIOaguOW/g+iDveWKm1xuICBjb25zdCBjYXBhYmlsaXRpZXMgPSBbXG4gICAge1xuICAgICAgaWNvbjogRGF0YWJhc2UsXG4gICAgICB0aXRsZTogXCLmlbDmja7lhajnlJ/lkb3lkajmnJ/nrqHnkIZcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIuS7juaVsOaNrumHh+mbhuOAgea4hea0l+OAgeWtmOWCqOWIsOW6lOeUqOeahOWFqOa1geeoi+euoeeQhu+8jOehruS/neaVsOaNrui0qOmHj+WSjOWuieWFqOaAp1wiLFxuICAgICAgZ3JhZGllbnQ6IFwiZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMFwiXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBCYXJDaGFydDMsXG4gICAgICB0aXRsZTogXCLmmbrog73mlbDmja7liIbmnpBcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIuWkmue7tOW6puaVsOaNruWIhuaekOWSjOWPr+inhuWMluWxleekuu+8jOaUr+aMgeWunuaXtuebkeaOp+WSjOmihOa1i+WIhuaekFwiLFxuICAgICAgZ3JhZGllbnQ6IFwiZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDBcIlxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgdGl0bGU6IFwi5pWw5o2u5a6J5YWo5rK755CGXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLlrozlloTnmoTmlbDmja7lronlhajkvZPns7vlkozotKjph4/nrqHmjqfmnLrliLbvvIzkv53pmpzmlbDmja7lkIjop4Tkvb/nlKhcIixcbiAgICAgIGdyYWRpZW50OiBcImZyb20tcHVycGxlLTUwMCB0by12aW9sZXQtNTAwXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IEdsb2JlLFxuICAgICAgdGl0bGU6IFwi6Leo6YOo6Zeo5pWw5o2u5YWx5LqrXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLmiZPnoLTmlbDmja7lraTlspvvvIzlrp7njrDmlL/liqHmlbDmja7nu5/kuIDlhbHkuqvlkozljY/lkIzlupTnlKhcIixcbiAgICAgIGdyYWRpZW50OiBcImZyb20tb3JhbmdlLTUwMCB0by1yZWQtNTAwXCJcbiAgICB9XG4gIF1cblxuICAvLyDop6PlhrPnmoTlnLrmma9cbiAgY29uc3Qgc2NlbmFyaW9zID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiBcIuaUv+W6nOWGs+etluaUr+aSkVwiLFxuICAgICAgZGVzY3JpcHRpb246IFwi5Li66aKG5a+85bGC5o+Q5L6b5a6e5pe244CB5YeG56Gu55qE5pWw5o2u5YiG5p6Q77yM5pSv5pKR56eR5a2m5Yaz562WXCIsXG4gICAgICBpY29uOiBUcmVuZGluZ1VwXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogXCLot6jpg6jpl6jljY/lkIxcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIua2iOmZpOS/oeaBr+WtpOWym++8jOWunueOsOmDqOmXqOmXtOaVsOaNruaXoOe8neWvueaOpeWSjOS4muWKoeWNj+WQjFwiLFxuICAgICAgaWNvbjogR2xvYmVcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiBcIuWFrOWFseacjeWKoeS8mOWMllwiLFxuICAgICAgZGVzY3JpcHRpb246IFwi6YCa6L+H5pWw5o2u5YiG5p6Q5LyY5YyW5YWs5YWx5pyN5Yqh5rWB56iL77yM5o+Q5Y2H5biC5rCR5ruh5oSP5bqmXCIsXG4gICAgICBpY29uOiBVc2Vyc1xuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6IFwi55uR566h5pWI6IO95o+Q5Y2HXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCLliKnnlKjlpKfmlbDmja7mioDmnK/mj5DljYfnm5HnrqHmlYjnjoflkoznsr7lh4bluqZcIixcbiAgICAgIGljb246IEV5ZVxuICAgIH1cbiAgXVxuXG4gIC8vIOaVsOaNruadpea6kOWNleS9jVxuICBjb25zdCBkYXRhU291cmNlVW5pdHMgPSBbXG4gICAgXCLluILmlL/lupzlip7lhazljoVcIiwgXCLlj5HlsZXmlLnpnanlp5RcIiwgXCLmlZnogrLlsYBcIiwgXCLnp5HmioDlsYBcIiwgXCLlt6Xkv6HlsYBcIiwgXCLlhazlronlsYBcIixcbiAgICBcIuawkeaUv+WxgFwiLCBcIuWPuOazleWxgFwiLCBcIui0ouaUv+WxgFwiLCBcIuS6uuekvuWxgFwiLCBcIuiHqueEtui1hOa6kOWxgFwiLCBcIueUn+aAgeeOr+Wig+WxgFwiLFxuICAgIFwi5L2P5bu65bGAXCIsIFwi5Lqk6YCa6L+Q6L6T5bGAXCIsIFwi5rC05Yqh5bGAXCIsIFwi5Yac5Lia5Yac5p2R5bGAXCIsIFwi5ZWG5Yqh5bGAXCIsIFwi5paH5peF5bGAXCIsXG4gICAgXCLljavlgaXlp5RcIiwgXCLlupTmgKXnrqHnkIblsYBcIiwgXCLlrqHorqHlsYBcIiwgXCLluILlnLrnm5HnrqHlsYBcIiwgXCLnu5/orqHlsYBcIiwgXCLljLvkv53lsYBcIlxuICBdXG5cbiAgLy8g5p2D5aiB5pWw5o2u6aKG5Z+fXG4gIGNvbnN0IGF1dGhvcml0eURhdGEgPSBbXG4gICAgeyBhcmVhOiBcIuS6uuWPo+S/oeaBr1wiLCBjb3ZlcmFnZTogXCIxMDAlXCIsIHNvdXJjZTogXCLlhazlronjgIHmsJHmlL/jgIHkurrnpL7nrYnpg6jpl6hcIiwgY29sb3I6IFwiYmx1ZVwiIH0sXG4gICAgeyBhcmVhOiBcIuS8geS4muS/oeaBr1wiLCBjb3ZlcmFnZTogXCIxMDAlXCIsIHNvdXJjZTogXCLluILlnLrnm5HnrqHjgIHnqI7liqHjgIHlt6Xkv6HnrYnpg6jpl6hcIiwgY29sb3I6IFwiZ3JlZW5cIiB9LFxuICAgIHsgYXJlYTogXCLlnLDnkIbkv6Hmga9cIiwgY292ZXJhZ2U6IFwiMTAwJVwiLCBzb3VyY2U6IFwi6Ieq54S26LWE5rqQ44CB5L2P5bu644CB5Lqk6YCa562J6YOo6ZeoXCIsIGNvbG9yOiBcInB1cnBsZVwiIH0sXG4gICAgeyBhcmVhOiBcIue7j+a1juaVsOaNrlwiLCBjb3ZlcmFnZTogXCI5NSVcIiwgc291cmNlOiBcIue7n+iuoeOAgei0ouaUv+OAgeWPkeaUueetiemDqOmXqFwiLCBjb2xvcjogXCJvcmFuZ2VcIiB9LFxuICAgIHsgYXJlYTogXCLnpL7kvJrkuovkuJpcIiwgY292ZXJhZ2U6IFwiOTAlXCIsIHNvdXJjZTogXCLmlZnogrLjgIHljavlgaXjgIHmlofml4XnrYnpg6jpl6hcIiwgY29sb3I6IFwicGlua1wiIH0sXG4gICAgeyBhcmVhOiBcIueOr+Wig+aVsOaNrlwiLCBjb3ZlcmFnZTogXCIxMDAlXCIsIHNvdXJjZTogXCLnlJ/mgIHnjq/looPjgIHmsLTliqHjgIHlupTmgKXnrYnpg6jpl6hcIiwgY29sb3I6IFwidGVhbFwiIH1cbiAgXVxuXG4gIC8vIOebruagh+WuouaIt1xuICBjb25zdCB0YXJnZXRVc2VycyA9IFtcbiAgICB7IFxuICAgICAgdHlwZTogXCLmlL/lupzlhrPnrZblsYJcIiwgXG4gICAgICBkZXNjOiBcIuS4uuWQhOe6p+mihuWvvOaPkOS+m+aVsOaNruaUr+aSkeWSjOWIhuaekOaKpeWRiu+8jOi+heWKqeenkeWtpuWGs+etllwiLFxuICAgICAgaWNvbjogQXdhcmQsXG4gICAgICBmZWF0dXJlczogW1wi5a6e5pe25pWw5o2u5aSn5bGPXCIsIFwi5Yaz562W5YiG5p6Q5oql5ZGKXCIsIFwi6LaL5Yq/6aKE5rWL5YiG5p6QXCJdXG4gICAgfSxcbiAgICB7IFxuICAgICAgdHlwZTogXCLkuJrliqHpg6jpl6hcIiwgXG4gICAgICBkZXNjOiBcIuWQhOWnlOWKnuWxgOaXpeW4uOS4muWKoeaVsOaNrueuoeeQhuWSjOi3qOmDqOmXqOWNj+WQjOW6lOeUqFwiLFxuICAgICAgaWNvbjogQnVpbGRpbmcyLFxuICAgICAgZmVhdHVyZXM6IFtcIuS4muWKoeaVsOaNrueuoeeQhlwiLCBcIui3qOmDqOmXqOWNj+WQjFwiLCBcIua1geeoi+S8mOWMllwiXVxuICAgIH0sXG4gICAgeyBcbiAgICAgIHR5cGU6IFwi5oqA5pyv5Lq65ZGYXCIsIFxuICAgICAgZGVzYzogXCLmlbDmja7lvIDlj5HjgIHov5Dnu7TlkozmioDmnK/mlK/mjIHkurrlkZjnmoTkuJPkuJrlt6XlhbdcIixcbiAgICAgIGljb246IENwdSxcbiAgICAgIGZlYXR1cmVzOiBbXCLmlbDmja7lvIDlj5Hlt6XlhbdcIiwgXCLns7vnu5/nm5HmjqdcIiwgXCLmioDmnK/mlK/mjIFcIl1cbiAgICB9LFxuICAgIHsgXG4gICAgICB0eXBlOiBcIuWFrOS8l+acjeWKoVwiLCBcbiAgICAgIGRlc2M6IFwi5Li65biC5rCR5ZKM5LyB5Lia5o+Q5L6b5L6/5rCR5pyN5Yqh5ZKM5L+h5oGv5p+l6K+iXCIsXG4gICAgICBpY29uOiBVc2VycyxcbiAgICAgIGZlYXR1cmVzOiBbXCLkv6Hmga/mn6Xor6JcIiwgXCLlnKjnur/mnI3liqFcIiwgXCLkvr/msJHlupTnlKhcIl1cbiAgICB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdmlhLWJsdWUtNTAgdG8taW5kaWdvLTUwXCI+XG4gICAgICB7Lyog6IOM5pmv6KOF6aWwICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHBvaW50ZXItZXZlbnRzLW5vbmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC00MCAtcmlnaHQtNDAgdy04MCBoLTgwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTQwMC8yMCB0by1pbmRpZ28tNDAwLzIwIHJvdW5kZWQtZnVsbCBibHVyLTN4bCBhbmltYXRlLWZsb2F0XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS00MCAtbGVmdC00MCB3LTgwIGgtODAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWluZGlnby00MDAvMjAgdG8tcHVycGxlLTQwMC8yMCByb3VuZGVkLWZ1bGwgYmx1ci0zeGwgYW5pbWF0ZS1mbG9hdFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMnMnIH19PjwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiPlxuICAgICAgICB7Lyog5Li76KaB5YaF5a655Yy65Z+fICovfVxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC02IHB5LTIwXCI+XG4gICAgICAgICAgey8qIOiLsembhOWMuuWfnyAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0yMCBhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBiZy1ibHVlLTUwLzUwIGJhY2tkcm9wLWJsdXItc20gcHgtNiBweS0zIHJvdW5kZWQtZnVsbCBtYi04XCI+XG4gICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS03MDBcIj7mmbrmhafmlL/liqEgwrcg5pWw5o2u6amx5YqoIMK3IOWIm+aWsOacquadpTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC02eGwgbWQ6dGV4dC03eGwgZm9udC1ib2xkIG1iLThcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWluZGlnby02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj7kupHlrofmlL/mlbDlubPlj7A8L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCB0ZXh0LWdyYXktNjAwIG1iLTEyIG1heC13LTR4bCBteC1hdXRvIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICDnu5/kuIDnmoTmlL/liqHmlbDmja7nrqHnkIbkuI7mnI3liqHlubPlj7DvvIzkuLrmlL/lupzmlbDlrZfljJbovazlnovmj5DkvpvlhajmlrnkvY3nmoTmlbDmja7mlK/mkpHvvIxcbiAgICAgICAgICAgICAg5a6e546w6Leo6YOo6Zeo5pWw5o2u5YWx5Lqr44CB5pm66IO95YiG5p6Q5Yaz562W5ZKM6auY5pWI5pS/5Yqh5pyN5YqhXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC04IG1iLTEyIHRleHQtbGcgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj4yNOS4quWnlOWKnuWxgOaOpeWFpTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPjIuNFRC5pWw5o2u5a2Y5YKoPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+OTkuOSXns7vnu5/lj6/nlKjmgKc8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIOaguOW/g+iDveWKmyAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJtYi0yMCBhbmltYXRlLXNsaWRlLXVwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+5bmz5Y+w5qC45b+D6IO95YqbPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwXCI+5Li65pS/5Yqh5pWw5a2X5YyW6L2s5Z6L5o+Q5L6b5YWo5pa55L2N55qE5oqA5pyv5pSv5pKRPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNSBnYXAtOFwiPlxuICAgICAgICAgICAgICB7Y2FwYWJpbGl0aWVzLm1hcCgoY2FwYWJpbGl0eSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gY2FwYWJpbGl0eS5pY29uXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjYXBhYmlsaXR5LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIHAtOCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBncm91cCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6IGAke2luZGV4ICogMTAwfW1zYCB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tYnIgJHtjYXBhYmlsaXR5LmdyYWRpZW50fSByb3VuZGVkLTJ4bCBteC1hdXRvIG1iLTYgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnIGdyb3VwLWhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtIGdyb3VwLWhvdmVyOnNjYWxlLTExMGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+e2NhcGFiaWxpdHkudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGxlYWRpbmctcmVsYXhlZFwiPntjYXBhYmlsaXR5LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuXG4gICAgICAgICAgICAgIHsvKiDov5vlhaXlubPlj7DmjInpkq7ljaHniYcgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwIHRvLWluZGlnby02MDAgcm91bmRlZC0yeGwgcC04IHNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBncm91cCBib3JkZXIgYm9yZGVyLWJsdWUtMzAwLzUwIGN1cnNvci1wb2ludGVyIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzQwMG1zJyB9fVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldFNob3dNb2R1bGVPdmVybGF5KHRydWUpfVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IHNldFNob3dNb2R1bGVPdmVybGF5KGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXdoaXRlLzIwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgbXgtYXV0byBtYi02IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZyBncm91cC1ob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRyYW5zZm9ybSBncm91cC1ob3ZlcjpzY2FsZS0xMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+56uL5Y2z5L2T6aqMPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwIGxlYWRpbmctcmVsYXhlZCBtYi00XCI+5oKs5YGc5p+l55yL5Lia5Yqh5qih5Z2X77yM54K55Ye76L+b5YWl5LqR5a6H5pS/5pWw5bmz5Y+wPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIGZsZXggaXRlbXMtY2VudGVyIGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi10cmFuc2Zvcm1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAg6L+b5YWl5bmz5Y+wXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cInctNSBoLTUgbWwtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICAgICAgey8qIOS4muWKoeaooeWdl+aCrOa1ruWxgiAqL31cbiAgICAgICAgICAgICAgICB7c2hvd01vZHVsZU92ZXJsYXkgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LWZ1bGwgbWwtNCB3LVs4MDBweF0gYmctd2hpdGUvOTUgYmFja2Ryb3AtYmx1ci14bCByb3VuZGVkLTN4bCBzaGFkb3ctMnhsIGJvcmRlciBib3JkZXItd2hpdGUvNTAgcC04IHotNTAgYW5pbWF0ZS1mYWRlLWluXCJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBzZXRTaG93TW9kdWxlT3ZlcmxheSh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRTaG93TW9kdWxlT3ZlcmxheShmYWxzZSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+5Lia5Yqh5qih5Z2X5b+r6YCf5YWl5Y+jPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+6YCJ5oup5oKo6ZyA6KaB55qE5Yqf6IO95qih5Z2X77yM55u05o6l6L+b5YWl5a+55bqU55qE5Lia5Yqh5pON5L2c55WM6Z2iPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7YnVzaW5lc3NNb2R1bGVzLm1hcCgobW9kdWxlLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IG1vZHVsZS5pY29uXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGluayBrZXk9e21vZHVsZS5uYW1lfSBocmVmPXttb2R1bGUuaHJlZn0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCByb3VuZGVkLTJ4bCBwLTQgaG92ZXI6Ymctd2hpdGUgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cCBjdXJzb3ItcG9pbnRlciBib3JkZXIgYm9yZGVyLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLWJyICR7bW9kdWxlLmdyYWRpZW50fSByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1tZCBncm91cC1ob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e21vZHVsZS5sb2dvfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21vZHVsZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMiBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttb2R1bGUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttb2R1bGUuZmVhdHVyZXMuc2xpY2UoMCwgMikubWFwKChmZWF0dXJlLCBpZHgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpZHh9IGNsYXNzTmFtZT1cInRleHQteHMgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCBweC0yIHB5LTEgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRyYW5zZm9ybSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1pbmRpZ28tNjAwIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQteGwgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPui/m+WFpeS4u+aOp+WPsDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiDop6PlhrPnmoTlnLrmma8gKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItMjAgYW5pbWF0ZS1zbGlkZS11cFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMjAwbXMnIH19PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPuino+WGs+eahOW6lOeUqOWcuuaZrzwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMFwiPuimhuebluaUv+WKoeeuoeeQhueahOWQhOS4quWFs+mUrueOr+iKgu+8jOaPkOWNh+aUv+W6nOayu+eQhuaViOiDvTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLThcIj5cbiAgICAgICAgICAgICAge3NjZW5hcmlvcy5tYXAoKHNjZW5hcmlvLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IEljb24gPSBzY2VuYXJpby5pY29uXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtzY2VuYXJpby50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLTJ4bCBwLTggc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgZ3JvdXAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBgJHtpbmRleCAqIDEwMH1tc2AgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNTAwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItM1wiPntzY2VuYXJpby50aXRsZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBsZWFkaW5nLXJlbGF4ZWRcIj57c2NlbmFyaW8uZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICB7Lyog5pWw5o2u5p2l5rqQ5Y2V5L2NICovfVxuICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cIm1iLTIwIGFuaW1hdGUtc2xpZGUtdXBcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzQwMG1zJyB9fT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMTJcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj7mlbDmja7mnaXmupDljZXkvY08L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDBcIj7opobnm5blhajluIIyNOS4quS4u+imgeWnlOWKnuWxgO+8jOaehOW7uue7n+S4gOeahOaVsOaNrueUn+aAgeS9k+ezuzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0zeGwgcC04IHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBsZzpncmlkLWNvbHMtNiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIHtkYXRhU291cmNlVW5pdHMubWFwKCh1bml0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e3VuaXR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNCByb3VuZGVkLXhsIGJnLWdyYXktNTAvNTAgaG92ZXI6YmctYmx1ZS01MC81MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aW5kZXggKiA1MH1tc2AgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nMiBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTUwMCBteC1hdXRvIG1iLTMgdHJhbnNpdGlvbi1jb2xvcnNcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPnt1bml0fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiDmnYPlqIHmlbDmja7opobnm5YgKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItMjAgYW5pbWF0ZS1zbGlkZS11cFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnNjAwbXMnIH19PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPuadg+WogeaVsOaNruimhuebljwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMFwiPuWFqOmdouimhuebluaUv+WKoeaguOW/g+aVsOaNrumihuWfn++8jOehruS/neaVsOaNruadg+WogeaAp+WSjOWujOaVtOaApzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAge2F1dGhvcml0eURhdGEubWFwKChkYXRhLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17ZGF0YS5hcmVhfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLTJ4bCBwLTggc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgZ3JvdXAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aW5kZXggKiAxMDB9bXNgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPntkYXRhLmFyZWF9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj57ZGF0YS5jb3ZlcmFnZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNlwiPntkYXRhLnNvdXJjZX08L3A+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8taW5kaWdvLTUwMCBoLTMgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTEwMDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBkYXRhLmNvdmVyYWdlIH19XG4gICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiDnm67moIflrqLmiLcgKi99XG4gICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItMjAgYW5pbWF0ZS1zbGlkZS11cFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnODAwbXMnIH19PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPuacjeWKoeWvueixoTwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTYwMFwiPuS4uuS4jeWQjOexu+Wei+eUqOaIt+aPkOS+m+S4k+S4muWMlueahOaVsOaNruacjeWKoeWSjOino+WGs+aWueahiDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLThcIj5cbiAgICAgICAgICAgICAge3RhcmdldFVzZXJzLm1hcCgodXNlciwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gdXNlci5pY29uXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXt1c2VyLnR5cGV9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgcC04IHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwIGdyb3VwIHRleHQtY2VudGVyIGJvcmRlciBib3JkZXItd2hpdGUvMjBcIlxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb25EZWxheTogYCR7aW5kZXggKiAxMDB9bXNgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1pbmRpZ28tNTAwIHJvdW5kZWQtMnhsIG14LWF1dG8gbWItNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+e3VzZXIudHlwZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTYgbGVhZGluZy1yZWxheGVkXCI+e3VzZXIuZGVzY308L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXIuZmVhdHVyZXMubWFwKChmZWF0dXJlLCBpZHgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpZHh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTUwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIOS4muWKoeaooeWdl+WvvOiIqiAtIOaYvuecvOeahOWFpeWPo+WMuuWfnyAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJtYi0yMCBhbmltYXRlLXNsaWRlLXVwXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcxMDAwbXMnIH19PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTUwIHB4LTggcHktNCByb3VuZGVkLTJ4bCBtYi04XCI+XG4gICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8taW5kaWdvLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPuS4muWKoeaooeWdl+WFpeWPozwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi02XCI+6YCJ5oup5oKo6ZyA6KaB55qE5Yqf6IO95qih5Z2XPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgdGV4dC1ncmF5LTYwMFwiPueCueWHu+S4i+aWueaooeWdl+WNoeeJh++8jOebtOaOpei/m+WFpeWvueW6lOeahOS4muWKoeaTjeS9nOeVjOmdojwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog56qB5Ye65pi+56S655qE5qih5Z2X572R5qC8ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAvMTAgdG8taW5kaWdvLTUwMC8xMCByb3VuZGVkLTN4bCBwLTEyIGJvcmRlci0yIGJvcmRlci1ibHVlLTIwMC81MCBzaGFkb3ctMnhsXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmlbDmja7lpKflsY8nLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+WunuaXtuaVsOaNruWPr+inhuWMluWxleekuu+8jOaUr+aMgeWkmuenjeWbvuihqOWSjOaMh+agh+ebkeaOpycsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL3NjcmVlbicsXG4gICAgICAgICAgICAgICAgICAgIGljb246IE1vbml0b3IsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiAnZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+WunuaXtuebkeaOpycsICflj6/op4bljJblm77ooagnLCAn5aSn5bGP5bGV56S6J11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmiqXooajliIbmnpAnLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+Wkmue7tOW6puaVsOaNruWIhuaekOaKpeihqO+8jOaUr+aMgeiHquWumuS5ieaKpeihqOeUn+aIkCcsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL3JlcG9ydHMnLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBGaWxlVGV4dCxcbiAgICAgICAgICAgICAgICAgICAgZ3JhZGllbnQ6ICdmcm9tLWdyZWVuLTUwMCB0by1lbWVyYWxkLTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+Wkmue7tOWIhuaekCcsICfoh6rlrprkuYnmiqXooagnLCAn5pWw5o2u5a+85Ye6J11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmlbDmja7ph4fpm4YnLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+Wkmua6kOaVsOaNrumHh+mbhuaOpeWFpe+8jOaUr+aMgeWunuaXtuWSjOaJuemHj+aVsOaNruWvvOWFpScsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL2NvbGxlY3Rpb24nLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBEYXRhYmFzZSxcbiAgICAgICAgICAgICAgICAgICAgZ3JhZGllbnQ6ICdmcm9tLXB1cnBsZS01MDAgdG8tdmlvbGV0LTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+Wkmua6kOaOpeWFpScsICflrp7ml7bph4fpm4YnLCAn5pWw5o2u5riF5rSXJ11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmlbDmja7msYfogZonLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+i3qOezu+e7n+aVsOaNruaVtOWQiOaxh+iBmu+8jOe7n+S4gOaVsOaNruagh+WHhuWSjOagvOW8jycsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL2FnZ3JlZ2F0aW9uJyxcbiAgICAgICAgICAgICAgICAgICAgaWNvbjogR2l0TWVyZ2UsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiAnZnJvbS1vcmFuZ2UtNTAwIHRvLWFtYmVyLTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+aVsOaNruaVtOWQiCcsICfmoIflh4bljJYnLCAn6LSo6YeP5o6n5Yi2J11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfmlbDmja7msrvnkIYnLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+aVsOaNrui0qOmHj+euoeaOp++8jOaVsOaNrua4hea0l+OAgeWOu+mHjeOAgeagh+WHhuWMluWkhOeQhicsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL2dvdmVybmFuY2UnLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBTaGllbGQsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiAnZnJvbS1yZWQtNTAwIHRvLXJvc2UtNTAwJyxcbiAgICAgICAgICAgICAgICAgICAgZmVhdHVyZXM6IFsn6LSo6YeP566h5o6nJywgJ+aVsOaNrua4hea0lycsICflkIjop4TnrqHnkIYnXVxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+i1hOa6kOaxoOeuoeeQhicsXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5pWw5o2u6LWE5rqQ57uf5LiA566h55CG77yM6LWE5rqQ55uu5b2V5ZKM5p2D6ZmQ5o6n5Yi2JyxcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogJy9kYXNoYm9hcmQvcmVzb3VyY2VzJyxcbiAgICAgICAgICAgICAgICAgICAgaWNvbjogSGFyZERyaXZlLFxuICAgICAgICAgICAgICAgICAgICBncmFkaWVudDogJ2Zyb20taW5kaWdvLTUwMCB0by1ibHVlLTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+i1hOa6kOebruW9lScsICfmnYPpmZDnrqHnkIYnLCAn5a2Y5YKo5LyY5YyWJ11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICforr7lpIfnm5HmjqcnLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+acjeWKoeWZqOiuvuWkh+WunuaXtuebkeaOp++8jOaAp+iDveaMh+agh+WSjOWRiuitpueuoeeQhicsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL21vbml0b3JpbmcnLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBTZXJ2ZXIsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiAnZnJvbS10ZWFsLTUwMCB0by1jeWFuLTUwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzOiBbJ+WunuaXtuebkeaOpycsICfmgKfog73liIbmnpAnLCAn5ZGK6K2m566h55CGJ11cbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6ICfnvZHnu5znrqHnkIYnLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ+WkmueJqeeQhue9kee7nOeuoeeQhu+8jOe9kee7nOaLk+aJkeWSjOa1gemHj+ebkeaOpycsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6ICcvZGFzaGJvYXJkL25ldHdvcmsnLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBOZXR3b3JrLFxuICAgICAgICAgICAgICAgICAgICBncmFkaWVudDogJ2Zyb20tcGluay01MDAgdG8tcm9zZS01MDAnLFxuICAgICAgICAgICAgICAgICAgICBmZWF0dXJlczogWyfnvZHnu5zmi5PmiZEnLCAn5rWB6YeP55uR5o6nJywgJ+WuieWFqOeuoeeQhiddXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIF0ubWFwKChtb2R1bGUsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gbW9kdWxlLmljb25cbiAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGtleT17bW9kdWxlLm5hbWV9IGhyZWY9e21vZHVsZS5ocmVmfT5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZS85MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtM3hsIHAtOCBzaGFkb3cteGwgaG92ZXI6c2hhZG93LTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgY3Vyc29yLXBvaW50ZXIgZ3JvdXAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBib3JkZXIgYm9yZGVyLXdoaXRlLzUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBgJHtpbmRleCAqIDEwMH1tc2AgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog6IOM5pmv5riQ5Y+Y5pWI5p6cICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgJHttb2R1bGUuZ3JhZGllbnR9IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi01MDAgcm91bmRlZC0zeGxgfT48L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMjAgaC0yMCBiZy1ncmFkaWVudC10by1iciAke21vZHVsZS5ncmFkaWVudH0gcm91bmRlZC0zeGwgbXgtYXV0byBtYi02IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZyBncm91cC1ob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRyYW5zZm9ybSBncm91cC1ob3ZlcjpzY2FsZS0xMTBgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctMTAgaC0xMCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTQgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21vZHVsZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttb2R1bGUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21vZHVsZS5mZWF0dXJlcy5tYXAoKGZlYXR1cmUsIGlkeCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aWR4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyZWVuLTUwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtYmx1ZS02MDAgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDnq4vljbPkvb/nlKhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy01IGgtNSBtbC0yIHRyYW5zZm9ybSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOaCrOa1ruaXtueahOWFieaViCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy1mdWxsIGgtMSBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLXdoaXRlLzUwIHRvLXRyYW5zcGFyZW50IHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtZnVsbCBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC1mdWxsIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTEwMDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5bqV6YOo5o+Q56S6ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG10LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxBd2FyZCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj7pgInmi6nku7vmhI/mqKHlnZflvIDlp4vmgqjnmoTmlbDmja7nrqHnkIbkuYvml4U8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIOW6lemDqOihjOWKqOWPt+WPrCAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBhbmltYXRlLXNsaWRlLXVwXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcxMjAwbXMnIH19PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8taW5kaWdvLTYwMCByb3VuZGVkLTN4bCBwLTEyIHRleHQtd2hpdGUgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIG1iLTZcIj7lh4blpIflpb3lvIDlp4vkuoblkJfvvJ88L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtYi0xMiB0ZXh0LWJsdWUtMTAwXCI+XG4gICAgICAgICAgICAgICAg56uL5Y2z5L2T6aqM5LqR5a6H5pS/5pWw5bmz5Y+w77yM5byA5ZCv5oKo55qE5pm65oWn5pS/5Yqh5pWw5o2u566h55CG5LmL5peFXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS15LTYgc206c3BhY2UteS0wIHNtOnNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNTAgZm9udC1ib2xkIHB5LTUgcHgtMTIgcm91bmRlZC0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCBmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXhsXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cInctNiBoLTYgbXItM1wiIC8+XG4gICAgICAgICAgICAgICAgICDov5vlhaXlubPlj7BcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMC8yMCBiYWNrZHJvcC1ibHVyLXNtIHRleHQtd2hpdGUgaG92ZXI6YmctYmx1ZS01MDAvMzAgZm9udC1tZWRpdW0gcHktNSBweC0xMiByb3VuZGVkLTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXhsXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy02IGgtNiBtci0zXCIgLz5cbiAgICAgICAgICAgICAgICAgIOS6huino+abtOWkmlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cbiAgICAgICAgPC9tYWluPlxuXG4gICAgICAgIHsvKiDpobXohJogKi99XG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci14bCBib3JkZXItdCBib3JkZXItd2hpdGUvMjAgbXQtMjBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTYgcHktMTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIG1iLTNcIj7CqSAyMDI0IOS6keWuh+aUv+aVsOW5s+WPsC4g5L+d55WZ5omA5pyJ5p2D5YipLjwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1iYXNlXCI+5Li65pS/5bqc5pWw5a2X5YyW6L2s5Z6L5o+Q5L6b5LiT5Lia55qE5pWw5o2u566h55CG5pyN5YqhPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZm9vdGVyPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwidXNlU3RhdGUiLCJTaGllbGQiLCJEYXRhYmFzZSIsIkJhckNoYXJ0MyIsIlVzZXJzIiwiQnVpbGRpbmcyIiwiR2xvYmUiLCJBcnJvd1JpZ2h0IiwiQ2hlY2tDaXJjbGUiLCJNb25pdG9yIiwiRmlsZVRleHQiLCJHaXRNZXJnZSIsIlNlcnZlciIsIk5ldHdvcmsiLCJIYXJkRHJpdmUiLCJTcGFya2xlcyIsIlRhcmdldCIsIkF3YXJkIiwiWmFwIiwiVHJlbmRpbmdVcCIsIkV5ZSIsIkNwdSIsIkhvbWVQYWdlIiwic2hvd01vZHVsZU92ZXJsYXkiLCJzZXRTaG93TW9kdWxlT3ZlcmxheSIsImJ1c2luZXNzTW9kdWxlcyIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImhyZWYiLCJpY29uIiwiZ3JhZGllbnQiLCJsb2dvIiwiZmVhdHVyZXMiLCJjYXBhYmlsaXRpZXMiLCJ0aXRsZSIsInNjZW5hcmlvcyIsImRhdGFTb3VyY2VVbml0cyIsImF1dGhvcml0eURhdGEiLCJhcmVhIiwiY292ZXJhZ2UiLCJzb3VyY2UiLCJjb2xvciIsInRhcmdldFVzZXJzIiwidHlwZSIsImRlc2MiLCJkaXYiLCJjbGFzc05hbWUiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwibWFpbiIsInNlY3Rpb24iLCJzcGFuIiwiaDEiLCJwIiwiaDIiLCJtYXAiLCJjYXBhYmlsaXR5IiwiaW5kZXgiLCJJY29uIiwiaDMiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJtb2R1bGUiLCJoNCIsInNsaWNlIiwiZmVhdHVyZSIsImlkeCIsInNjZW5hcmlvIiwidW5pdCIsImRhdGEiLCJ3aWR0aCIsInVzZXIiLCJidXR0b24iLCJmb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});