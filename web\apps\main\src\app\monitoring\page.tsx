'use client'

import Link from 'next/link'
import { 
  Server, 
  Cpu, 
  HardDrive, 
  Activity,
  Wifi,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Plus,
  ArrowRight,
  BarChart3,
  Clock,
  Thermometer,
  Zap
} from 'lucide-react'

export default function MonitoringSystemPage() {
  const servers = [
    {
      id: 1,
      name: 'DB-Server-01',
      type: '数据库服务器',
      ip: '************',
      status: 'online',
      cpu: 45,
      memory: 68,
      disk: 72,
      network: 'normal',
      uptime: '15天',
      location: '机房A-01'
    },
    {
      id: 2,
      name: 'WEB-Server-02',
      type: 'Web服务器',
      ip: '************',
      status: 'warning',
      cpu: 85,
      memory: 92,
      disk: 45,
      network: 'normal',
      uptime: '8天',
      location: '机房A-02'
    },
    {
      id: 3,
      name: 'APP-Server-03',
      type: '应用服务器',
      ip: '************',
      status: 'offline',
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 'error',
      uptime: '0天',
      location: '机房B-01'
    },
    {
      id: 4,
      name: 'BACKUP-Server-04',
      type: '备份服务器',
      ip: '************',
      status: 'online',
      cpu: 25,
      memory: 35,
      disk: 88,
      network: 'normal',
      uptime: '32天',
      location: '机房B-02'
    }
  ]

  const quickStats = [
    { label: '在线设备', value: '24', trend: '+2', icon: Server, color: 'teal' },
    { label: '告警数量', value: '3', trend: '-1', icon: AlertTriangle, color: 'orange' },
    { label: '平均负载', value: '65%', trend: '+5%', icon: Activity, color: 'blue' },
    { label: '网络状态', value: '正常', trend: '稳定', icon: Wifi, color: 'green' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'offline': return <XCircle className="w-5 h-5 text-red-500" />
      default: return <XCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return '在线'
      case 'warning': return '告警'
      case 'offline': return '离线'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-700'
      case 'warning': return 'bg-yellow-100 text-yellow-700'
      case 'offline': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return 'bg-red-500'
    if (usage >= 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">设备监控管理中心</h1>
        <p className="text-xl text-gray-600">实时监控服务器设备状态，及时发现和处理异常</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-teal-600 text-white px-6 py-3 rounded-xl hover:bg-teal-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加设备</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <BarChart3 className="w-5 h-5" />
            <span>性能报告</span>
          </button>
        </div>
      </div>

      {/* 服务器列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {servers.map((server, index) => (
          <div
            key={server.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{server.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(server.status)}`}>
                      {getStatusText(server.status)}
                    </span>
                    {getStatusIcon(server.status)}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <span className="bg-gray-100 px-2 py-1 rounded-full">{server.type}</span>
                    <span>{server.ip}</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">{server.location}</span>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Server className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* 性能指标 */}
              <div className="space-y-3 mb-4">
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <div className="flex items-center space-x-1">
                      <Cpu className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">CPU</span>
                    </div>
                    <span className="font-medium">{server.cpu}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${getUsageColor(server.cpu)}`}
                      style={{ width: `${server.cpu}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <div className="flex items-center space-x-1">
                      <Activity className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">内存</span>
                    </div>
                    <span className="font-medium">{server.memory}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${getUsageColor(server.memory)}`}
                      style={{ width: `${server.memory}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <div className="flex items-center space-x-1">
                      <HardDrive className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-600">磁盘</span>
                    </div>
                    <span className="font-medium">{server.disk}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${getUsageColor(server.disk)}`}
                      style={{ width: `${server.disk}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* 运行时间 */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>运行时间: {server.uptime}</span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="text-teal-600 hover:text-teal-700 font-medium text-sm transition-colors">
                    重启
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    日志
                  </button>
                </div>
                <Link
                  href={`/monitoring/server/${server.id}`}
                  className="text-teal-600 hover:text-teal-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>详情</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
