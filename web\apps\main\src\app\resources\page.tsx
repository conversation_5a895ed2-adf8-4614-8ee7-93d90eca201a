'use client'

import Link from 'next/link'
import { 
  HardDrive, 
  Database, 
  Folder, 
  Users,
  Lock,
  Eye,
  Download,
  Upload,
  Plus,
  ArrowRight,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

export default function ResourcesSystemPage() {
  const resources = [
    {
      id: 1,
      name: '人口基础数据库',
      type: '数据库',
      size: '2.4TB',
      records: '2,400,000',
      department: '公安局',
      access: 'restricted',
      lastUpdated: '2024-01-15',
      downloads: 156,
      category: '基础数据'
    },
    {
      id: 2,
      name: '企业注册信息',
      type: '数据集',
      size: '156GB',
      records: '156,000',
      department: '市场监管局',
      access: 'public',
      lastUpdated: '2024-01-14',
      downloads: 89,
      category: '经济数据'
    },
    {
      id: 3,
      name: '地理信息数据',
      type: '空间数据',
      size: '890GB',
      records: '89,000',
      department: '自然资源局',
      access: 'internal',
      lastUpdated: '2024-01-13',
      downloads: 234,
      category: '空间数据'
    },
    {
      id: 4,
      name: '环境监测数据',
      type: '时序数据',
      size: '234GB',
      records: '2,340,000',
      department: '生态环境局',
      access: 'public',
      lastUpdated: '2024-01-12',
      downloads: 67,
      category: '环境数据'
    }
  ]

  const quickStats = [
    { label: '数据资源', value: '156', trend: '+12', icon: Database, color: 'indigo' },
    { label: '存储容量', value: '12.4TB', trend: '+8%', icon: HardDrive, color: 'blue' },
    { label: '访问次数', value: '2.4K', trend: '+15%', icon: Eye, color: 'green' },
    { label: '授权用户', value: '89', trend: '+5', icon: Users, color: 'purple' }
  ]

  const getAccessIcon = (access: string) => {
    switch (access) {
      case 'public': return <Eye className="w-4 h-4 text-green-500" />
      case 'internal': return <Users className="w-4 h-4 text-yellow-500" />
      case 'restricted': return <Lock className="w-4 h-4 text-red-500" />
      default: return <Lock className="w-4 h-4 text-gray-500" />
    }
  }

  const getAccessText = (access: string) => {
    switch (access) {
      case 'public': return '公开'
      case 'internal': return '内部'
      case 'restricted': return '受限'
      default: return '未知'
    }
  }

  const getAccessColor = (access: string) => {
    switch (access) {
      case 'public': return 'bg-green-100 text-green-700'
      case 'internal': return 'bg-yellow-100 text-yellow-700'
      case 'restricted': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">资源池管理中心</h1>
        <p className="text-xl text-gray-600">统一管理数据资源，控制访问权限，优化存储配置</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-indigo-600 text-white px-6 py-3 rounded-xl hover:bg-indigo-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>添加资源</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Upload className="w-5 h-5" />
            <span>批量导入</span>
          </button>
        </div>
      </div>

      {/* 资源列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {resources.map((resource, index) => (
          <div
            key={resource.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{resource.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAccessColor(resource.access)}`}>
                      {getAccessText(resource.access)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                    <span className="bg-gray-100 px-2 py-1 rounded-full">{resource.type}</span>
                    <span>{resource.department}</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">{resource.category}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getAccessIcon(resource.access)}
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Database className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">存储大小</p>
                  <p className="text-lg font-bold text-gray-900">{resource.size}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">记录数</p>
                  <p className="text-sm font-medium text-gray-900">{resource.records}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-600 mb-1">下载次数</p>
                  <p className="text-sm font-medium text-gray-900">{resource.downloads}</p>
                </div>
              </div>

              {/* 最后更新时间 */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>最后更新: {resource.lastUpdated}</span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    预览
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>下载</span>
                  </button>
                </div>
                <Link
                  href={`/resources/detail/${resource.id}`}
                  className="text-indigo-600 hover:text-indigo-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>详情</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
