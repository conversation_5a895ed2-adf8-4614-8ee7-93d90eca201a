"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/page",{

/***/ "(app-pages-browser)/./src/app/collection/page.tsx":
/*!*************************************!*\
  !*** ./src/app/collection/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tree-pine.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wheat.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Globe,GraduationCap,Heart,Home,MapPin,Plus,Scale,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CollectionSystemPage() {\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"compact\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"all\");\n    // 全市24个数据来源单位及其采集的数据类型\n    const dataSourceUnits = [\n        {\n            id: 1,\n            name: \"市政府办公厅\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"45K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"政务公开数据\",\n                    records: \"15K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"会议纪要数据\",\n                    records: \"20K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公文流转数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            name: \"发展改革委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"128K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"项目投资数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"经济指标数据\",\n                    records: \"42K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"规划数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"价格监测数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            name: \"教育局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            status: \"warning\",\n            totalSources: 5,\n            activeSources: 4,\n            totalRecords: \"892K\",\n            lastSync: \"2小时前\",\n            dataTypes: [\n                {\n                    name: \"学生学籍数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"教师信息数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"学校基础数据\",\n                    records: \"234K\",\n                    status: \"error\",\n                    type: \"API\"\n                },\n                {\n                    name: \"考试成绩数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"教育资源数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            name: \"科技局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"67K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"科技项目数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专利申请数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"科技企业数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            name: \"工信局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"1分钟前\",\n            dataTypes: [\n                {\n                    name: \"工业企业数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"产业园区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"信息化项目数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"节能减排数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            name: \"公安局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"2.4M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"人口基础信息\",\n                    records: \"1.2M\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"户籍管理数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"身份证数据\",\n                    records: \"567K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"治安管理数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"案件管理数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 7,\n            name: \"民政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"婚姻登记数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社会救助数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"养老服务数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会组织数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 8,\n            name: \"司法局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"78K\",\n            lastSync: \"12分钟前\",\n            dataTypes: [\n                {\n                    name: \"法律援助数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公证服务数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"人民调解数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 9,\n            name: \"财政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"预算执行数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"政府采购数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"国有资产数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"财政收支数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项资金数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 10,\n            name: \"人社局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 5,\n            totalRecords: \"1.2M\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"就业登记数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社保缴费数据\",\n                    records: \"389K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人才信息数据\",\n                    records: \"234K\",\n                    status: \"warning\",\n                    type: \"API\"\n                },\n                {\n                    name: \"职业培训数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"劳动关系数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"工资指导数据\",\n                    records: \"9K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 11,\n            name: \"自然资源局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"9分钟前\",\n            dataTypes: [\n                {\n                    name: \"土地利用数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"不动产登记数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"地理信息数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"矿产资源数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 12,\n            name: \"生态环境局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"234K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"空气质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水质监测数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"污染源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"环评审批数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"生态保护数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        // 继续添加剩余的12个单位\n        {\n            id: 13,\n            name: \"住建局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"456K\",\n            lastSync: \"11分钟前\",\n            dataTypes: [\n                {\n                    name: \"房屋建筑数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"城市规划数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"建筑许可数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"物业管理数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 14,\n            name: \"交通运输局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"789K\",\n            lastSync: \"2分钟前\",\n            dataTypes: [\n                {\n                    name: \"道路运输数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共交通数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通基础设施数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"运输企业数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"31K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 15,\n            name: \"水务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"供水管网数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"污水处理数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水资源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"防汛数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 16,\n            name: \"农业农村局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 3,\n            totalRecords: \"345K\",\n            lastSync: \"15分钟前\",\n            dataTypes: [\n                {\n                    name: \"农业生产数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"农村土地数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"农产品质量数据\",\n                    records: \"67K\",\n                    status: \"warning\",\n                    type: \"File\"\n                },\n                {\n                    name: \"农业补贴数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 17,\n            name: \"商务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"123K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"对外贸易数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"招商引资数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"商贸流通数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 18,\n            name: \"文旅局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"10分钟前\",\n            dataTypes: [\n                {\n                    name: \"旅游景区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"文化场馆数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"文物保护数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 19,\n            name: \"卫健委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"1.5M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"医疗机构数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医护人员数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共卫生数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"疫情防控数据\",\n                    records: \"289K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"健康档案数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"医疗服务数据\",\n                    records: \"53K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 20,\n            name: \"应急管理局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"167K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"安全生产数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"应急预案数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"灾害监测数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"应急物资数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 21,\n            name: \"审计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"20分钟前\",\n            dataTypes: [\n                {\n                    name: \"审计项目数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"审计结果数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"整改跟踪数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 22,\n            name: \"市场监管局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"企业注册数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"食品安全数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"产品质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"价格监管数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"知识产权数据\",\n                    records: \"54K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 23,\n            name: \"统计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"经济统计数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人口统计数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会统计数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项调查数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 24,\n            name: \"医保局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"医保参保数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医保结算数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"定点医疗机构数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"药品目录数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        }\n    ];\n    // 总体统计数据\n    const overallStats = [\n        {\n            label: \"数据来源单位\",\n            value: \"24\",\n            trend: \"全覆盖\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            label: \"数据源总数\",\n            value: \"156\",\n            trend: \"+12\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"在线数据源\",\n            value: \"142\",\n            trend: \"91%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"今日采集量\",\n            value: \"2.8TB\",\n            trend: \"+15%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            color: \"orange\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 32\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 28\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 30\n                }, this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"正常运行\";\n            case \"error\":\n                return \"连接异常\";\n            case \"warning\":\n                return \"部分异常\";\n            case \"syncing\":\n                return \"同步中\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"error\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            case \"syncing\":\n                return \"bg-blue-100 text-blue-700 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    const getDataTypeIcon = (type)=>{\n        switch(type){\n            case \"Database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 31\n                }, this);\n            case \"API\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 26\n                }, this);\n            case \"File\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto px-6 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"全市数据采集概览\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"24个数据来源单位的数据采集情况实时监控\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: overallStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-\".concat(stat.color, \"-500 to-\").concat(stat.color, \"-600 rounded-xl flex items-center justify-center\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"添加数据源\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"批量同步\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"最后更新: \",\n                            new Date().toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4\",\n                children: dataSourceUnits.map((unit, index)=>{\n                    const Icon = unit.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"min-w-0 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-bold text-gray-900 truncate\",\n                                                            children: unit.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-1.5 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                    children: getStatusText(unit.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                getStatusIcon(unit.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right text-xs text-gray-600\",\n                                            children: unit.lastSync\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2 mb-3 p-2 bg-gray-50/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"总数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-gray-900\",\n                                                    children: unit.totalSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"在线\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-green-600\",\n                                                    children: unit.activeSources\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"数据量\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-blue-600\",\n                                                    children: unit.totalRecords\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xs font-semibold text-gray-700\",\n                                            children: [\n                                                \"数据类型 (\",\n                                                unit.dataTypes.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-32 overflow-y-auto space-y-1\",\n                                            children: unit.dataTypes.map((dataType, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-white/50 rounded border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 min-w-0 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 flex-shrink-0\",\n                                                                    children: [\n                                                                        getDataTypeIcon(dataType.type),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: dataType.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                    children: dataType.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                getStatusIcon(dataType.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right flex-shrink-0 ml-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-semibold text-gray-900\",\n                                                                children: dataType.records\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, idx, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                    children: \"详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                    children: \"同步\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/collection/unit/\".concat(unit.id),\n                                            className: \"text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Globe_GraduationCap_Heart_Home_MapPin_Plus_Scale_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 15\n                        }, this)\n                    }, unit.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 543,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemPage, \"IGmnZS9Y1IFhVtN0SFrVv9COoy8=\");\n_c = CollectionSystemPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/page.tsx\n"));

/***/ })

});