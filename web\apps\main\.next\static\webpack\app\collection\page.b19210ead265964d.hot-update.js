"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/collection/page",{

/***/ "(app-pages-browser)/./src/app/collection/page.tsx":
/*!*************************************!*\
  !*** ./src/app/collection/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollectionSystemPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tree-pine.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wheat.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,AlertTriangle,ArrowRight,BarChart3,Briefcase,Building2,Camera,Car,CheckCircle,Clock,CreditCard,Database,Droplets,Eye,FileText,Filter,Globe,GraduationCap,Grid3X3,Heart,Home,List,MapPin,Plus,Scale,Search,Server,Shield,ShoppingBag,Stethoscope,TreePine,TrendingUp,Users,Wheat,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CollectionSystemPage() {\n    _s();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"compact\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"all\");\n    // 全市24个数据来源单位及其采集的数据类型\n    const dataSourceUnits = [\n        {\n            id: 1,\n            name: \"市政府办公厅\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"45K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"政务公开数据\",\n                    records: \"15K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"会议纪要数据\",\n                    records: \"20K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公文流转数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 2,\n            name: \"发展改革委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"128K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"项目投资数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"经济指标数据\",\n                    records: \"42K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"规划数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"价格监测数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 3,\n            name: \"教育局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            status: \"warning\",\n            totalSources: 5,\n            activeSources: 4,\n            totalRecords: \"892K\",\n            lastSync: \"2小时前\",\n            dataTypes: [\n                {\n                    name: \"学生学籍数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"教师信息数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"学校基础数据\",\n                    records: \"234K\",\n                    status: \"error\",\n                    type: \"API\"\n                },\n                {\n                    name: \"考试成绩数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"教育资源数据\",\n                    records: \"35K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 4,\n            name: \"科技局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"67K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"科技项目数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专利申请数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"科技企业数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 5,\n            name: \"工信局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"1分钟前\",\n            dataTypes: [\n                {\n                    name: \"工业企业数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"产业园区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"信息化项目数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"节能减排数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 6,\n            name: \"公安局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"2.4M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"人口基础信息\",\n                    records: \"1.2M\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"户籍管理数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"身份证数据\",\n                    records: \"567K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"治安管理数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"案件管理数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 7,\n            name: \"民政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"婚姻登记数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社会救助数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"养老服务数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会组织数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 8,\n            name: \"司法局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"78K\",\n            lastSync: \"12分钟前\",\n            dataTypes: [\n                {\n                    name: \"法律援助数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公证服务数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"人民调解数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 9,\n            name: \"财政局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"预算执行数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"政府采购数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"国有资产数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"财政收支数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项资金数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 10,\n            name: \"人社局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 5,\n            totalRecords: \"1.2M\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"就业登记数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"社保缴费数据\",\n                    records: \"389K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人才信息数据\",\n                    records: \"234K\",\n                    status: \"warning\",\n                    type: \"API\"\n                },\n                {\n                    name: \"职业培训数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"劳动关系数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"工资指导数据\",\n                    records: \"9K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 11,\n            name: \"自然资源局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"9分钟前\",\n            dataTypes: [\n                {\n                    name: \"土地利用数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"不动产登记数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"地理信息数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"矿产资源数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 12,\n            name: \"生态环境局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"234K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"空气质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水质监测数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"污染源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"环评审批数据\",\n                    records: \"23K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"生态保护数据\",\n                    records: \"10K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        // 继续添加剩余的12个单位\n        {\n            id: 13,\n            name: \"住建局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"456K\",\n            lastSync: \"11分钟前\",\n            dataTypes: [\n                {\n                    name: \"房屋建筑数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"城市规划数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"建筑许可数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"物业管理数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 14,\n            name: \"交通运输局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"789K\",\n            lastSync: \"2分钟前\",\n            dataTypes: [\n                {\n                    name: \"道路运输数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共交通数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"交通基础设施数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"运输企业数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"交通违法数据\",\n                    records: \"31K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 15,\n            name: \"水务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"234K\",\n            lastSync: \"8分钟前\",\n            dataTypes: [\n                {\n                    name: \"供水管网数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"污水处理数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"水资源数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"防汛数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 16,\n            name: \"农业农村局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 3,\n            totalRecords: \"345K\",\n            lastSync: \"15分钟前\",\n            dataTypes: [\n                {\n                    name: \"农业生产数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"农村土地数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"农产品质量数据\",\n                    records: \"67K\",\n                    status: \"warning\",\n                    type: \"File\"\n                },\n                {\n                    name: \"农业补贴数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 17,\n            name: \"商务局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"123K\",\n            lastSync: \"6分钟前\",\n            dataTypes: [\n                {\n                    name: \"对外贸易数据\",\n                    records: \"56K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"招商引资数据\",\n                    records: \"34K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"商贸流通数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 18,\n            name: \"文旅局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"10分钟前\",\n            dataTypes: [\n                {\n                    name: \"旅游景区数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"文化场馆数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"文物保护数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 19,\n            name: \"卫健委\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            status: \"connected\",\n            totalSources: 6,\n            activeSources: 6,\n            totalRecords: \"1.5M\",\n            lastSync: \"实时\",\n            dataTypes: [\n                {\n                    name: \"医疗机构数据\",\n                    records: \"456K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医护人员数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"公共卫生数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"疫情防控数据\",\n                    records: \"289K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"健康档案数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"医疗服务数据\",\n                    records: \"53K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 20,\n            name: \"应急管理局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"167K\",\n            lastSync: \"5分钟前\",\n            dataTypes: [\n                {\n                    name: \"安全生产数据\",\n                    records: \"78K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"应急预案数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"灾害监测数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"应急物资数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                }\n            ]\n        },\n        {\n            id: 21,\n            name: \"审计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            status: \"connected\",\n            totalSources: 3,\n            activeSources: 3,\n            totalRecords: \"89K\",\n            lastSync: \"20分钟前\",\n            dataTypes: [\n                {\n                    name: \"审计项目数据\",\n                    records: \"45K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"审计结果数据\",\n                    records: \"28K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"整改跟踪数据\",\n                    records: \"16K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 22,\n            name: \"市场监管局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            status: \"connected\",\n            totalSources: 5,\n            activeSources: 5,\n            totalRecords: \"567K\",\n            lastSync: \"4分钟前\",\n            dataTypes: [\n                {\n                    name: \"企业注册数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"食品安全数据\",\n                    records: \"123K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"产品质量数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"价格监管数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"File\"\n                },\n                {\n                    name: \"知识产权数据\",\n                    records: \"54K\",\n                    status: \"connected\",\n                    type: \"API\"\n                }\n            ]\n        },\n        {\n            id: 23,\n            name: \"统计局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"345K\",\n            lastSync: \"7分钟前\",\n            dataTypes: [\n                {\n                    name: \"经济统计数据\",\n                    records: \"156K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"人口统计数据\",\n                    records: \"89K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"社会统计数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"专项调查数据\",\n                    records: \"33K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        },\n        {\n            id: 24,\n            name: \"医保局\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            status: \"connected\",\n            totalSources: 4,\n            activeSources: 4,\n            totalRecords: \"678K\",\n            lastSync: \"3分钟前\",\n            dataTypes: [\n                {\n                    name: \"医保参保数据\",\n                    records: \"345K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"医保结算数据\",\n                    records: \"234K\",\n                    status: \"connected\",\n                    type: \"API\"\n                },\n                {\n                    name: \"定点医疗机构数据\",\n                    records: \"67K\",\n                    status: \"connected\",\n                    type: \"Database\"\n                },\n                {\n                    name: \"药品目录数据\",\n                    records: \"32K\",\n                    status: \"connected\",\n                    type: \"File\"\n                }\n            ]\n        }\n    ];\n    // 总体统计数据\n    const overallStats = [\n        {\n            label: \"数据来源单位\",\n            value: \"24\",\n            trend: \"全覆盖\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"purple\"\n        },\n        {\n            label: \"数据源总数\",\n            value: \"156\",\n            trend: \"+12\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"在线数据源\",\n            value: \"142\",\n            trend: \"91%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"今日采集量\",\n            value: \"2.8TB\",\n            trend: \"+15%\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            color: \"orange\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 32\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 28\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 30\n                }, this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"正常运行\";\n            case \"error\":\n                return \"连接异常\";\n            case \"warning\":\n                return \"部分异常\";\n            case \"syncing\":\n                return \"同步中\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"connected\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"error\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            case \"syncing\":\n                return \"bg-blue-100 text-blue-700 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    const getDataTypeIcon = (type)=>{\n        switch(type){\n            case \"Database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 31\n                }, this);\n            case \"API\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 26\n                }, this);\n            case \"File\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"全市数据采集概览\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"24个数据来源单位的数据采集情况实时监控\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: overallStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-\".concat(stat.color, \"-500 to-\").concat(stat.color, \"-600 rounded-xl flex items-center justify-center\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 border border-white/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索数据来源单位...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm w-full sm:w-64\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                            className: \"w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            className: \"border border-gray-200 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"全部状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"connected\",\n                                                    children: \"正常运行\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"warning\",\n                                                    children: \"部分异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"error\",\n                                                    children: \"连接异常\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 bg-gray-100 rounded-lg p-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"compact\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"compact\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(viewMode === \"grid\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 ml-4\",\n                                    children: [\n                                        \"最后更新: \",\n                                        new Date().toLocaleString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-purple-600 text-white px-6 py-3 rounded-xl hover:bg-purple-700 transition-colors flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"添加数据源\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"批量同步\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                lineNumber: 590,\n                columnNumber: 7\n            }, this),\n            (()=>{\n                const filteredUnits = dataSourceUnits.filter((unit)=>{\n                    const matchesSearch = unit.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesStatus = statusFilter === \"all\" || unit.status === statusFilter;\n                    return matchesSearch && matchesStatus;\n                });\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"显示 \",\n                                        filteredUnits.length,\n                                        \" / \",\n                                        dataSourceUnits.length,\n                                        \" 个数据来源单位\",\n                                        searchTerm && ' \\xb7 搜索: \"'.concat(searchTerm, '\"'),\n                                        statusFilter !== \"all\" && \" \\xb7 状态: \".concat(getStatusText(statusFilter))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"视图模式: \",\n                                        viewMode === \"compact\" ? \"紧凑\" : \"详细\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 \".concat(viewMode === \"compact\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6\" : \"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5\"),\n                            children: filteredUnits.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-full text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"w-12 h-12 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                        children: \"未找到匹配的数据来源单位\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"请尝试调整搜索条件或过滤器\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 17\n                            }, this) : filteredUnits.map((unit)=>{\n                                const Icon = unit.icon;\n                                return viewMode === \"compact\" ? // 紧凑视图模式\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/20 hover:shadow-md transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 bg-gradient-to-br from-purple-500 to-violet-500 rounded flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-3 h-3 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"min-w-0 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xs font-bold text-gray-900 truncate\",\n                                                                    children: unit.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 655,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                            children: getStatusText(unit.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        getStatusIcon(unit.status)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-1 mb-2 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900\",\n                                                                children: unit.totalSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"在线\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-green-600\",\n                                                                children: unit.activeSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-blue-600\",\n                                                                children: unit.totalRecords\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-1 bg-gray-50/50 rounded text-xs text-gray-600 mb-2\",\n                                                children: [\n                                                    unit.dataTypes.length,\n                                                    \" 种数据类型\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                        children: \"详情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/collection/unit/\".concat(unit.id),\n                                                        className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                        children: \"管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 15\n                                    }, this)\n                                }, unit.id, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, this) : // 标准视图模式\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-4 h-4 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0 flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-bold text-gray-900 truncate\",\n                                                                        children: unit.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-1.5 py-0.5 rounded text-xs font-medium border \".concat(getStatusColor(unit.status)),\n                                                                                children: getStatusText(unit.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 717,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            getStatusIcon(unit.status)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right text-xs text-gray-600\",\n                                                        children: unit.lastSync\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-2 mb-3 p-2 bg-gray-50/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"总数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-gray-900\",\n                                                                children: unit.totalSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"在线\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-green-600\",\n                                                                children: unit.activeSources\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"数据量\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-bold text-blue-600\",\n                                                                children: unit.totalRecords\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xs font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"数据类型 (\",\n                                                            unit.dataTypes.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-32 overflow-y-auto space-y-1\",\n                                                        children: unit.dataTypes.map((dataType, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-2 bg-white/50 rounded border border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 min-w-0 flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1 flex-shrink-0\",\n                                                                                children: [\n                                                                                    getDataTypeIcon(dataType.type),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: dataType.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                        lineNumber: 754,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 752,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-medium text-gray-900 truncate\",\n                                                                                children: dataType.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                                lineNumber: 756,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            getStatusIcon(dataType.status)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right flex-shrink-0 ml-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold text-gray-900\",\n                                                                            children: dataType.records\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-purple-600 hover:text-purple-700 font-medium text-xs transition-colors\",\n                                                                children: \"详情\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium text-xs transition-colors\",\n                                                                children: \"同步\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: \"/collection/unit/\".concat(unit.id),\n                                                        className: \"text-purple-600 hover:text-purple-700 font-medium flex items-center space-x-1 transition-colors text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"管理\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_AlertTriangle_ArrowRight_BarChart3_Briefcase_Building2_Camera_Car_CheckCircle_Clock_CreditCard_Database_Droplets_Eye_FileText_Filter_Globe_GraduationCap_Grid3X3_Heart_Home_List_MapPin_Plus_Scale_Search_Server_Shield_ShoppingBag_Stethoscope_TreePine_TrendingUp_Users_Wheat_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, this)\n                                }, unit.id, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 13\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true);\n            })()\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\collection\\\\page.tsx\",\n        lineNumber: 494,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionSystemPage, \"IGmnZS9Y1IFhVtN0SFrVv9COoy8=\");\n_c = CollectionSystemPage;\nvar _c;\n$RefreshReg$(_c, \"CollectionSystemPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/collection/page.tsx\n"));

/***/ })

});